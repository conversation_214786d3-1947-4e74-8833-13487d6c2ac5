const reasonDeletingAccount =
    'Please let us know the reason for deleting the account and any suggestions on what we can do to improve the experience';
const cancelMembership =
    'Deleting your account will permanently remove your profile and all associated data from our app. This action is irreversible, and the following consequences will occur:';
const cancelMembershipPoints = [
    "1. This is a no-obligation network and you can simply ignore any requests that you don't care for.  It's like LinkedIn in that regard.  Our hope is that every once in a while you may see a request that intrigues you.",
    '2.  If you delete your account, your membership will automatically terminate (with no refunds).',
    '3. You can mute yourself or any/all of your clubs for a period of time if you need to from account settings.',
    '4.  You can manage your notification preferences and stop Email, Text or Push notifications from account settings.',
    '5.  You can define a smaller set of clubs that you are willing to see requests from (in your favorites).',
];
const CANCEL_MEMBERSHIP_POINTS = [
    {
        title: '1. Connections and interactions:',
        subTitle: [
            'All your TG connections with other users will be severed.',
            'Messages, conversations, and any shared content with other users or on Feed will be lost.',
        ],
    },
    {
        title: '2. Groups you have created:',
        subTitle: [
            'All TG groups you have created will be deleted where you are the sole creator and members of these groups will no longer have access to them.',
            'Any content, discussions, or shared resources within these groups will be lost.',
        ],
    },
    {
        title: '3. Account information and settings:',
        subTitle: [
            'Your personal information, including your username, email address, and profile details, will be removed.',
        ],
    },
    {
        title: '4. Termination of Membership:',
        subTitle: [
            'Deleting your account will result in the automatic termination of your membership. Please note that no refunds will be provided in such cases.',
        ],
    },
];
const deleteAccountFirstScreenContent = [
    {
        title: '1. Super Flexibility in Game Requests:',
        subTitle:
            'Our network operates on a non-obligatory basis, allowing you the freedom to disregard any Game Requests that do not interest you. Similar to LinkedIn, you have the autonomy to choose which requests to engage with. We hope that occasionally you may come across intriguing requests that pique your curiosity.',
    },
    {
        title: '2. Temporarily Mute Clubs:',
        subTitle:
            'If you need a break or wish to limit your interactions, you can utilize the "Mute" feature in your account settings. This allows you to temporarily mute yourself or specific clubs for a defined period.',
    },
    {
        title: '3. Customizing Notification Preferences:',
        subTitle:
            'You have the ability to manage your notification preferences according to your preferences. In your account settings, you can choose to disable email, text, or push notifications as desired.',
    },
    {
        title: '4. Favourites Clubs for Request Viewing:',
        subTitle:
            'To streamline your experience, you can select a smaller set of clubs that you are willing to receive play requests from. By adding clubs to your favourites, you will primarily see requests from these preferred clubs.',
    },
];
const screenContent = `You don’t have any pending friend requests ${`\n`} Please check back later.`;
const allFriendScreenContent = `You don’t have any TG friends yet ${`\n`} Please check back later.`;
const declinedScreenContent = `You don’t have any declined friend requests ${`\n`} Please check back later.`;
const newChatString = `You don't have any TG friends yet. Please start using our chat platform by adding a friend.`;
const blockedScreenEmptyStata = `You don't have any blocked users.`;
const deleteSecondScreenHeader =
    'Deleting your account will permanently remove your profile and all associated data from our app. This action is irreversible, and the following consequences will occur:';
const deleteFirstScreenHeader = 'Explore Alternatives before Deleting Your Account';
const deleteFirstScreenHeaderBody =
    'Before proceeding with deleting your account, we want to make sure you are aware of the following alternatives that may address any concerns you have:';
const deleteFirstScreenFooterText1 =
    'We encourage you to explore these alternatives before making the irreversible decision to delete your account.';
const deleteFirstScreenFooterText2 = 'Do you still wish to continue?';
const owner = 'owner';
const LEAVE_GROUP_TEXT = 'Are you sure you want to leave this Group ?';
const DELETE_CHAT_STRING = 'Are you sure you want to delete this chat?';
const DELETE_GROUP_STRING = 'Are you sure you want to delete this Group?';
const CREATE_GROUP_RADIO_BUTTON_DESCRIPTION = 'Does this group needs approval for users to join?';
const PENDING_MEMBER_REQUEST_EMPTY_STATE = 'You don’t have any Pending Member requests. Please check back later.';
const WITHDRAW_REQUEST = 'Withdraw Request';
const JOIN = 'Join';
const PAGINATION_LIMIT = 10;
const NO_GROUP_FOUND = 'No Groups Found';
const NO_RESULT_FOUND = 'No Results Found';
const GROUP_EMPTY_STATE_MESSAGE = 'Start by creating a Group and Joining others';
const GROUP_SEARCH_EMPTY_STATE_MESSAGE = 'No result found. Please try some other term';
const GROUPS = 'Groups';
const MY_TG_FRIENDS_SCREEN = 'MyTGFriendsScreen';
const MY_CONTACTS = 'MyContacts';
const GROUP_CREATED_POPUP_HEADER = 'Group Created Successfully';
const GROUP_CREATED_POPUP_BODY_TEXT = 'Do you wish to copy and share the group link?';
const MY_TG_GROUP_ROLES = { 1: 'Group Creator', 2: 'Group Admin', 3: 'Member' };
const VIEW_GROUP = 'View Group';
const EDIT_GROUP = 'Edit Group';
const MEMBER_REQUEST = 'Member Request';
const GO_TO_GROUP_CHAT = 'Go to Group Chat';
const SHARE_GROUP = 'Share Group';
const DELETE_GROUP = 'Delete Group';
const DELETE_CHAT = 'Delete Chat';
const LEAVE_GROUP = 'Leave Group';
const CREATE_OFFER = 'Create Offer';
const MY_GROUPS = 'My Groups';
const All_GROUPS = 'All Groups';
const CREATE_GROUP_NOTE =
    'This group will be discoverable via Search by All Thousand Greens members, they can join/send request to join the same';
const GROUP_SCREEN_LIST_LIMIT_CONSTANTS = 5;
const DELETE_ACCOUNT_HEADER = 'Explore Alternatives before Deleting Your Account';
const DELETE_CONFIRMATION_TEXT = 'Are you sure you want to delete this group?';
const groupDeletedText = "The Requested page is unavailable or doesn't exist.";
const WITHDRAW_REQUEST_CONFIRMATION_TEXT = 'Do you want to withdraw this request?';
const DECLINE_MEMBER_REQUEST_POPUP_HEADER = 'Decline Member Request';
const DECLINE_MEMBER_REQUEST_POPUP_TITLE = 'Are you sure you want to decline this request?';
const MY_CONTACT = 'MyContact';
const MY_TG_GROUP_LISTING = 'MyTgGroupListing';
const ACCOUNT_SETTING_CLUB_VISIBILITY_HEADER = 'Manage Club visibility in your profile page?';
const ACCOUNT_SETTING_TOOLTIP_TEXT =
    'With this setting you can control which clubs are visible to other Group members ( who are not your friends) when they view your profile from Group Member list. Your Friends can view all your Clubs.';
const MAX_TAGS_LENGTH = 15;
const TAG_ERROR =
    'Please ensure that tags consist of at least 3 characters and include only alphanumeric characters, underscores (_), and ampersands (&).';
const REQUESTS = 'Requests';
const REQUESTS_LOWER_CASE = 'requests';
const RECEIVED = 'Received';
const RECEIVED_LOWER_CASE = 'received';
const REQUESTED = 'Requested';
const REQUESTED_LOWER_CASE = 'requested';
const ACCEPTED = 'Accepted';
const ACCEPTED_LOWER_CASE = 'accepted';
const CAMERA_PERMISSION = 'Thousand Greens Require Camera Permission. Enable it in App Settings.';
const MEDIA_PERMISSION = 'Thousand Greens Require Media Permission. Enable it in App Settings.';
const ADMIN_USER_ID = '10c0a827-2985-4a6e-8b97-aa4422f78879';
const POPULAR = 'POPULAR';
const HIGHLY_REQUESTED = 'HIGHLY REQUESTED';
const UNEXPLORED = 'UNEXPLORED';
const ACCOUNT_SETTING_SCREEN_TEXT =
    'Joining of Private network has been disabled since this feature is going to get replaced by Group functionality soon!';
const ACCOUNT_SETTING_TOOLTIP_STRING = 'NGV is the difference between games played as a guest and games hosted.';
const ERROR = 'error';
const SUCCESS = 'success';
const INFO = 'info';
const TOAST_ERROR_MESSAGE = 'something went wrong';
const NGV_SCREEN_STRING =
    "NGV reflects guest games played vs games hosted. FTR represents the count of first-time requesters you have hosted. Renewal pricing is based on a member's NGV and FTR over the past 12 months.";
const NET_GAME_VALUE = 'Net Game Value';
const YOUR_CURRENT_NGV = 'Your Current NGV';
const CLUB_AND_GOLFER_INFO = 'Golf Profile';
const ADDRESS_DETAILS = 'Address Details';
const ADDRESS_NOT_ADDED = 'Address not added';
const VIEW_HISTORY = 'View History';
const PERSONAL_INFORMATION = 'Personal Information';
const EDIT_PROFILE = 'Edit Profile';
const PROFILE = 'Profile';
const PROFILE_DETAILS = 'Profile Details';
const ACCOUNT_SETTING_TEXT = 'This setting allows you to control who can send you a Game request on the TG platform';
const LIMIT_FILTER_PLAY_REQUESTS = 'Limit/Filter play requests';
const ACCOUNT_SETTING_LIMIT_FILTER_TEXT =
    'For each of your selected clubs, restrict incoming requests from members of your favourited clubs';
const MUTE_ACCOUNT_DESCRIPTION =
    'You will not be able to create or receive any Game Request while your account is muted';
const REFERRAL_TEXT = "View the list of friends you've referred to the Thousand Greens app.";
const SUPER_HOST = 'Super Host';
const FOUNDER_CLUB_MEMBER = 'Founder Club Member';
const NOTIFICATION_SCREEN_STRING =
    'You need to have at least one of the Email, Text, or Push notifications as turned on at any moment';
const MUTE_EVENTS_DESCRIPTION_TEXT = 'This will stop notifications for new events and benefits.';
const OVERRIDE_GROUP_SETTING = 'Over-ride individual Group Settings';
const OVERRIDE_CONFIRMATION_TEXT =
    "Do you wish to override the club visibility settings you've applied individually on few of your group";
const MANAGE_CLUB_VISIBILITY_TEXT = "Manage My Clubs' visibility for all my Groups";
const MANAGE_CLUB_VISIBILITY_FOR_GROUP_TEXT = 'Manage Club(s) Visibility for this Group';
const MANAGE_CLUB_VISIBILITY_SUBTEXT =
    "All checked clubs below will be visible to all TG users belonging to same group as you irrespective of their tier and club's defined LTV settings";
const MANAGE_CLUB_VISIBILITY_FOR_GROUP_SUBTEXT =
    "All checked clubs below will be visible to all TG users belonging to this group irrespective of their tier and club's defined LTV settings.";
const CLUB_OTHERS_MEMBERS_TEXT = 'Would you like your profile information to be visible to other members at this club?';
const TOGGLE_OFF_OF_CLUB_MEMBER_TEXT =
    "To connect with fellow members of this club, it's essential that you also make yourself visible to them. Kindly achieve this by activating the toggle below.";
const NO_OTHER_USER_WANT_SHARE_PROFILE_TEXT = 'No member has opted to share their profile information';
const NO_MEMBERS_FOUND = 'No other Thousand Greens Members found in your club';
const LOWER_TIER_TEXT = 'Making yourself visible to lower tier members enables them to make a request to you';
const MUTE_CLUB_STRING = 'Turning this on will stop any incoming request on this club for the chosen duration';
const MY_TG_TEXT = `If you uncheck this option, any TG member who is not part of your "My TG Community” will not be able to create game requests to you, You will however, have an option to create game requests to any eligible TG member`;
const MY_TG_COMMUNITY_TEXT = `This is your mandatory visibility in the application. Your “My TG Community” (Friends and group members as per Group Settings) will always be able to send you game requests`;
const CREATE_REQUEST_PLACEHOLDER =
    'Please type a courteous note introducing yourself and your request to potential hosts, a polite note improves acceptance chances';
const SEND_REQUEST_TO_TEXT =
    'This is your mandatory visibility in the application. You will always be visible to your “My TG Community” (Friends and group members as per Group Settings)';
const SEND_REQUEST_TO_TG_MEMBER = `If you uncheck this option, you will no longer be visible to any TG members who are not part of your "My TG Community"`;
const MUTE_CLUB_POPUP_TEXT = 'You cannot create the offer as your club is muted';
const RECEIVED_REQUEST_ONLY_TEXT = 'Receive Request only from My Favourite Club Members';
const FAVORITE_RESTRICTED_NOTE =
    "This offer would only be visible to the members of the clubs that you've marked as favorite.";
const CREATE_REQUEST_TOAST_MESSAGE =
    'There are no qualified hosts for this request. Please adjust your filter criteria and try again';
const FAV_CLUB_EMPTY_SCREEN_TEXT = "You don't have any favorite clubs.";
const OFFER_CREATION_NOTE = 'Offer created for: Members of Founders TG Group';
const EVENT_TG_COMMUNITY_TEXT =
    'This is your mandatory event visibility. All of your created events will always be visible to your friends and TG group members';
const OFFER_TG_COMMUNITY_TEXT =
    'This is your mandatory offer visibility. All of your created offers will always be visible to your friends and TG group members';
const EVENT_TG_ALL_MEMBER_TEXT =
    'If this is checked, then along with your TG Community, your event will be visible to all Thousand Greens Members';
const PLAY_AS_A_COUPLE = 'Play as couple';
const GENDER_PREFERENCE_SUBHEADER = 'Set up Gender preference to receive request on this club';
const GENDER_PREFERENCE_HEADER = 'Receive request on Gender Preferences';
const MUTE_ACCOUNT_POPUP_TEXT =
    'Your account is muted! Unmute to receive new game request at your club and to create offers.';
const DISMISS_TEXT = 'Dismiss this for next 30 days';
const DUPLICATE_PEGBOARD = 'Do you want to duplicate an existing Pegboard?';
const AUDIENCE = 'Audience (Create pegboard for)';
const PEGBOARD_INFO_TEXT = 'You can add clubs after creating pegboard';
const ADD_CLUB_PEGBOARD_TEXT = "You don't have any clubs added to this Pegboard yet. Start adding clubs now";
const NO_CLUB_IN_PEGBOARD = 'There are no clubs in the Pegboard yet.';
const DELETE_PEGBOARD_POPUP_TEXT = 'Are you sure you want to delete';
const ADD_NEW_CLUB_TEXT = 'This club is not yet in our database, please add an address';
const ADD_NEW_CLUB = 'Add New Club';
const PEGBOARD_EDIT_INFO_TEXT =
    'If you want to add/remove clubs in this pegboard, you can do the same by going into the Pegboard.';
const REQUEST_AGAINST_OFFER_TEXT =
    'Send request to other members at the club if offering member unresponsive for 24 hours';
const GROUPS_WITH_THE_MOST_MEMBERS = 'Groups with the most members';
const GROUPS_WITH_MY_FRIENDS = 'Groups with my friends';
const NEW_GROUPS_CREATED = 'New groups created';
const GROUPS_WITH_MOST_MUTUAL_MEMBERS = 'Groups with most mutual members';
const GROUPS_WITH_MOST_NEW_MEMBERS = 'Groups getting most new members';
const OFFER_CREATE_FOR_ALL_TG_MEMBER =
    'If this is checked, then along with your TG Community, your offer will be visible to all Thousand Greens Members';
const GENDER = {
    MALE: "male",
    FEMALE: "female",
    BOTH: "both"
}
const GROUP_NAME_PROMPT_TEXT = 'Do not use the terms Ambassador or Super Host. For geo specific groups, name the group TG Local: XXX'
const REFERRAL_DISCOUNT = 'Discounts will never allow member renewal pricing to drop below the Par Amount.'
const TG_MEMBER_REFER = 'If multiple TG members refer the same person, the membership discount will be awarded only to the first referrer.'
const CONTACT_INFO_STRING = 'Contact Information visibility'
const CONTACT_INFO_BODY = 'To make your contact information visible to other members of your club, activate the toggle below.'
const SUB_HEADER_EDIT_AMBASSADOR = 'This message will be sent to every new user with whom you get connected as their TG Ambassador'
const ERROR_404 = "The resource you were trying to find does not exist or has been deleted."
const GUIDE_HEADER_1 = "Manage your Profile with ease"
const GUIDE_HEADER_2 = "Stay updated with Notifications & Updates"
const GUIDE_HEADER_3 = "Quick Access, Simplified"
const GUIDE_DESCRIPTION_1 = "Tap your profile picture to view details, and access account settings"
const GUIDE_DESCRIPTION_2 = "Click card to view details or swipe left or right to dismiss."
const GUIDE_DESCRIPTION_3 = "Swipe up for a full menu of options or swipe down to focus on the main tabs."
const CHAT = "Chat"
const MUTED_ACCOUNT = "Muted Account"
const MUTED_CLUB = "Muted Club"
const MUTED_ACCOUNT_DESCRIPTION = "You have a muted club <clubname>. Go to club settings to unmute to receive upcoming game requests"
const MUTED_CLUB_DESCRIPTION = "You have one or more muted club(s). To receive upcoming Game requests on your club(s), deactivate the toggle below."
const LOG_SCREEN_HEADER = "Log A Played Game"
const LOG_SCREEN_BODY = "Here you can log a game that took place offline and was not scheduled through the TG App."
const LOG_SCREEN_BODY_2 = "Here you can log a game with another TG member that has already happened"
const RESULT_NOT_FOUND_TEXT = "Member with entered number/E-mail doesn’t exist in our system. Please recheck the entered details"
const NO_ACTIVE_REQUESTS = "You've no active requests at this time";
const NO_ACTIVE_REQUESTS_HISTORY = "You’ve no requests here at this time";
const NO_ACTIVE_OFFERS = "You don’t have any active offers right now";
const NO_OFFERS_AVAILABLE = "No offers available right now";
const FIRST_TIME_REQUESTER_MESSAGE = 'This user is sending a request for the first time on Thousand Greens.';
const DELETE_REQUEST_CONFIRMATION_TEXT = 'Are you sure you would like to remove this request from your history?';
const OFFLINE_GAME_LOG_WARNING_STRING =
    "We request you to use this feature only for games where no request was created on Thousand Greens' App. To properly account for cancelled requests or request where the host was different than the Accepting Host, please contact";
const REQUESTED_HISTORY = 'requested-history';
const GAME_REVIEW_PLACEHOLDER = 'Any observations on course architecture, club facilities and experience, notable signature food and drinks, etc. tend to be appreciated by other members';
const OFFLINE_GAME_LOG_SUCCESS_STRING = 'You’ve logged a offline played game in TG successfully. Your NGV would be updated in sometime, check back the NGV log screen in few minutes';
const GAME_INFORMATION = 'Game Information';
const REQUEST_INFORMATION = 'Request Information';
const GAME_DATE = 'Game Date';
const REQUESTED_DATE = 'Requested Date(s)';
const ACCOMPANIED_PLAY_ONLY = 'Accompanied play only';
const REQUEST_SENT_TO_MEMBERS_MEETING_THE_FOLLOWING_CRITERIA = 'Request sent to members meeting the following criteria:';
const DECLINING_REQUEST_LAST_MINUTE_SHORT_NOTICE = 'Canceling requests last minute/short notice are disruptive and we don\'t encourage.';
const REMOVE_REQUEST = 'Remove Request'
const MAP_CLUB_LIST_EMPTY_STATE = 'No clubs within viewport on the map'
const CLUB_PERCENTAGE_ACCEPTANCE = 'Club percentage acceptance'
const CLUB_PERCENTAGE = 'More than 25%'
const ALL = 'All'
const CLUB_MEMBER_COUNT = 'Club Member Count'
const MORE_THAN_10_MEMBERS = 'More than 10 members'
const CLUB_WITH_MY_TG_COMMUNITY = 'Club with My TG Community'
const CLUB_MEMBER_COUNT_FILTER = 'clubMemberCount'
const CLUB_PERCENTAGE_FILTER = 'clubPercentage'
const MUTE_ACCOUNT_HEADER_TEXT = 'By muting your entire account, you won’t be able to receive or create any new requests, and all request-related notifications would stop, even related to your active requests.'
const UNMUTE_ACCOUNT_HEADER_TEXT = 'Do you want to unmute profile?'
const DELETE_CLOSURE_PERIOD = 'Delete Closure Period'
const REMOVE_CLOSURE_CONFIRMATION_TEXT = 'Are you sure you would like to remove this closure period?'
const RESOURCE_NO_EXIST = 'The resource you were trying to find does not exist or has been deleted.'

export {
    reasonDeletingAccount,
    cancelMembership,
    cancelMembershipPoints,
    screenContent,
    allFriendScreenContent,
    declinedScreenContent,
    newChatString,
    blockedScreenEmptyStata,
    NO_GROUP_FOUND,
    GROUP_EMPTY_STATE_MESSAGE,
    GROUPS,
    MY_TG_FRIENDS_SCREEN,
    MY_CONTACTS,
    GROUP_CREATED_POPUP_HEADER,
    GROUP_CREATED_POPUP_BODY_TEXT,
    MY_TG_GROUP_ROLES,
    VIEW_GROUP,
    EDIT_GROUP,
    GO_TO_GROUP_CHAT,
    SHARE_GROUP,
    DELETE_GROUP,
    LEAVE_GROUP,
    MY_GROUPS,
    CREATE_GROUP_NOTE,
    GROUP_SCREEN_LIST_LIMIT_CONSTANTS,
    All_GROUPS,
    DELETE_ACCOUNT_HEADER,
    CANCEL_MEMBERSHIP_POINTS,
    deleteAccountFirstScreenContent,
    deleteFirstScreenHeader,
    deleteFirstScreenHeaderBody,
    deleteFirstScreenFooterText1,
    deleteFirstScreenFooterText2,
    owner,
    NO_RESULT_FOUND,
    GROUP_SEARCH_EMPTY_STATE_MESSAGE,
    LEAVE_GROUP_TEXT,
    DELETE_CONFIRMATION_TEXT,
    DELETE_CHAT_STRING,
    DELETE_GROUP_STRING,
    groupDeletedText,
    DELETE_CHAT,
    deleteSecondScreenHeader,
    CREATE_GROUP_RADIO_BUTTON_DESCRIPTION,
    MEMBER_REQUEST,
    PENDING_MEMBER_REQUEST_EMPTY_STATE,
    WITHDRAW_REQUEST,
    JOIN,
    WITHDRAW_REQUEST_CONFIRMATION_TEXT,
    PAGINATION_LIMIT,
    DECLINE_MEMBER_REQUEST_POPUP_HEADER,
    DECLINE_MEMBER_REQUEST_POPUP_TITLE,
    MY_CONTACT,
    MY_TG_GROUP_LISTING,
    ACCOUNT_SETTING_CLUB_VISIBILITY_HEADER,
    ACCOUNT_SETTING_TOOLTIP_TEXT,
    MAX_TAGS_LENGTH,
    TAG_ERROR,
    REQUESTS,
    RECEIVED,
    REQUESTED,
    CAMERA_PERMISSION,
    MEDIA_PERMISSION,
    ADMIN_USER_ID,
    POPULAR,
    UNEXPLORED,
    ACCOUNT_SETTING_SCREEN_TEXT,
    ACCOUNT_SETTING_TOOLTIP_STRING,
    ERROR,
    SUCCESS,
    INFO,
    TOAST_ERROR_MESSAGE,
    NGV_SCREEN_STRING,
    NET_GAME_VALUE,
    YOUR_CURRENT_NGV,
    CLUB_AND_GOLFER_INFO,
    VIEW_HISTORY,
    PERSONAL_INFORMATION,
    EDIT_PROFILE,
    PROFILE,
    PROFILE_DETAILS,
    ACCOUNT_SETTING_TEXT,
    LIMIT_FILTER_PLAY_REQUESTS,
    ACCOUNT_SETTING_LIMIT_FILTER_TEXT,
    MUTE_ACCOUNT_DESCRIPTION,
    REFERRAL_TEXT,
    SUPER_HOST,
    FOUNDER_CLUB_MEMBER,
    NOTIFICATION_SCREEN_STRING,
    MUTE_EVENTS_DESCRIPTION_TEXT,
    OVERRIDE_GROUP_SETTING,
    OVERRIDE_CONFIRMATION_TEXT,
    MANAGE_CLUB_VISIBILITY_TEXT,
    MANAGE_CLUB_VISIBILITY_SUBTEXT,
    MANAGE_CLUB_VISIBILITY_FOR_GROUP_SUBTEXT,
    MANAGE_CLUB_VISIBILITY_FOR_GROUP_TEXT,
    CLUB_OTHERS_MEMBERS_TEXT,
    TOGGLE_OFF_OF_CLUB_MEMBER_TEXT,
    NO_OTHER_USER_WANT_SHARE_PROFILE_TEXT,
    NO_MEMBERS_FOUND,
    LOWER_TIER_TEXT,
    MUTE_CLUB_STRING,
    MY_TG_TEXT,
    MY_TG_COMMUNITY_TEXT,
    CREATE_REQUEST_PLACEHOLDER,
    SEND_REQUEST_TO_TEXT,
    SEND_REQUEST_TO_TG_MEMBER,
    MUTE_CLUB_POPUP_TEXT,
    RECEIVED_REQUEST_ONLY_TEXT,
    FAVORITE_RESTRICTED_NOTE,
    CREATE_REQUEST_TOAST_MESSAGE,
    FAV_CLUB_EMPTY_SCREEN_TEXT,
    CREATE_OFFER,
    OFFER_CREATION_NOTE,
    EVENT_TG_COMMUNITY_TEXT,
    EVENT_TG_ALL_MEMBER_TEXT,
    PLAY_AS_A_COUPLE,
    GENDER_PREFERENCE_SUBHEADER,
    GENDER_PREFERENCE_HEADER,
    MUTE_ACCOUNT_POPUP_TEXT,
    DISMISS_TEXT,
    DUPLICATE_PEGBOARD,
    AUDIENCE,
    PEGBOARD_INFO_TEXT,
    ADD_CLUB_PEGBOARD_TEXT,
    DELETE_PEGBOARD_POPUP_TEXT,
    ADD_NEW_CLUB_TEXT,
    ADD_NEW_CLUB,
    PEGBOARD_EDIT_INFO_TEXT,
    REQUEST_AGAINST_OFFER_TEXT,
    NO_CLUB_IN_PEGBOARD,
    GROUPS_WITH_THE_MOST_MEMBERS,
    GROUPS_WITH_MY_FRIENDS,
    OFFER_CREATE_FOR_ALL_TG_MEMBER,
    OFFER_TG_COMMUNITY_TEXT,
    NEW_GROUPS_CREATED,
    GROUPS_WITH_MOST_MUTUAL_MEMBERS,
    GENDER,
    GROUPS_WITH_MOST_NEW_MEMBERS,
    GROUP_NAME_PROMPT_TEXT,
    REFERRAL_DISCOUNT,
    TG_MEMBER_REFER,
    CONTACT_INFO_STRING,
    CONTACT_INFO_BODY,
    SUB_HEADER_EDIT_AMBASSADOR,
    ERROR_404,
    HIGHLY_REQUESTED,
    GUIDE_HEADER_1,
    GUIDE_DESCRIPTION_1,
    GUIDE_HEADER_2,
    GUIDE_DESCRIPTION_2,
    GUIDE_HEADER_3,
    GUIDE_DESCRIPTION_3,
    CHAT,
    ADDRESS_DETAILS,
    MUTED_ACCOUNT,
    MUTED_ACCOUNT_DESCRIPTION,
    ADDRESS_NOT_ADDED,
    MUTED_CLUB,
    MUTED_CLUB_DESCRIPTION,
    LOG_SCREEN_HEADER,
    LOG_SCREEN_BODY,
    LOG_SCREEN_BODY_2,
    RESULT_NOT_FOUND_TEXT,
    NO_ACTIVE_REQUESTS,
    FIRST_TIME_REQUESTER_MESSAGE,
    ACCEPTED,
    DELETE_REQUEST_CONFIRMATION_TEXT,
    OFFLINE_GAME_LOG_WARNING_STRING,
    RECEIVED_LOWER_CASE,
    REQUESTED_LOWER_CASE,
    ACCEPTED_LOWER_CASE,
    REQUESTED_HISTORY,
    GAME_REVIEW_PLACEHOLDER,
    OFFLINE_GAME_LOG_SUCCESS_STRING,
    GAME_INFORMATION,
    REQUEST_INFORMATION,
    GAME_DATE,
    REQUESTED_DATE,
    ACCOMPANIED_PLAY_ONLY,
    REQUEST_SENT_TO_MEMBERS_MEETING_THE_FOLLOWING_CRITERIA,
    NO_ACTIVE_REQUESTS_HISTORY,
    DECLINING_REQUEST_LAST_MINUTE_SHORT_NOTICE,
    REMOVE_REQUEST,
    MAP_CLUB_LIST_EMPTY_STATE,
    CLUB_PERCENTAGE_ACCEPTANCE,
    CLUB_PERCENTAGE,
    ALL,
    CLUB_MEMBER_COUNT,
    MORE_THAN_10_MEMBERS,
    CLUB_WITH_MY_TG_COMMUNITY,
    CLUB_MEMBER_COUNT_FILTER,
    CLUB_PERCENTAGE_FILTER,
    MUTE_ACCOUNT_HEADER_TEXT,
    UNMUTE_ACCOUNT_HEADER_TEXT,
    NO_ACTIVE_OFFERS,
    DELETE_CLOSURE_PERIOD,
    REMOVE_CLOSURE_CONFIRMATION_TEXT,
    NO_OFFERS_AVAILABLE,
    RESOURCE_NO_EXIST
};
