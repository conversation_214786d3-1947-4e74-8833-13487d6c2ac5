import { Activity<PERSON>ndi<PERSON><PERSON>, But<PERSON>, Linking, PermissionsAndroid, Platform, StyleSheet, Text, View } from 'react-native';
import React, { memo, useContext, useEffect, useMemo, useRef, useState } from 'react';
import MapboxGL from '@rnmapbox/maps';
import { OnPressEvent } from '@rnmapbox/maps/lib/typescript/src/types/OnPressEvent';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import ScreenResizeButton from '../../../components/buttons/ScreenResizeButton';
import MapCluster from '../../../components/map/MapCluster';
import UserLocationButton from '../../../components/buttons/UserLocationButton';
import { GlobalContext } from '../../../context/contextApi';
// @ts-ignore
import { LocationContext } from '../../../context/LocationContext';
import { colors } from '../../../theme/theme';
import OfferListingScreen from './OfferListingScreen';
import { MapClub, MapClubDetail, Offer } from '../../../interface';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../../interface/type';

interface DateRange {
    startDate: string;
    endDate: string;
}

interface OfferViewProps {
    centerCoordinates: [number, number];
    setCenterCoordinates: (coordinates: [number, number]) => void;
    zoomLevel: number;
    setZoomLevel: (zoomLevel: number) => void;
    showSearch: boolean;
    setShowSearch: (showSearch: boolean) => void;
    offersMapMarkers: any;
    setMapBoundaries: (mapBoundaries: any) => void;
    mapBoundaries: any;
    activeView: string;
    selectedClub: MapClub | null;
    setSelectedClub: (selectedClub: MapClub | null) => void;
    searchTerm: string;
    locations: any;
    searchedClubList: any;
    setActiveView: (activeView: string) => void;
    selectedRegion: { lat: number; lng: number };
    setSelectedRegion: (region: { lat: number; lng: number } | null) => void;
    offers: Offer[];
    renderedOffers: MapClub[];
    dateRange: DateRange;
    setDateRange: (dateRange: DateRange) => void;
    searchedClub: MapClub | null;
    setSearchedClub: (searchedClub: MapClub | null) => void;
    routeClub: MapClub | null;
    isMarkerClicked: boolean;
    setIsMarkerClicked: (isMarkerClicked: boolean) => void;
    handleRequestAgainstOffer: (item: Offer) => void;
    handleEditOffer: (offer: Offer) => void;
    handleDeleteOffer: (offer: Offer) => void;
    handleOfferDetailPress: (item: Offer) => void;
    navigation: NavigationProp<RootStackParamList>;
    showFilterOption: boolean;
}

const OfferView = ({
    centerCoordinates,
    setCenterCoordinates,
    zoomLevel,
    setZoomLevel,
    showSearch,
    setShowSearch,
    offersMapMarkers,
    setMapBoundaries,
    mapBoundaries,
    activeView,
    selectedClub,
    setSelectedClub,
    searchTerm,
    locations,
    searchedClubList,
    setActiveView,
    selectedRegion,
    setSelectedRegion,
    offers,
    renderedOffers,
    dateRange,
    setDateRange,
    searchedClub,
    setSearchedClub,
    routeClub,
    isMarkerClicked,
    setIsMarkerClicked,
    handleRequestAgainstOffer,
    handleEditOffer,
    handleDeleteOffer,
    handleOfferDetailPress,
    navigation,
    showFilterOption,
}: OfferViewProps) => {
    const { state, actions } = useContext(GlobalContext);
    // @ts-ignore
    const { userLocation, getLocation } = useContext(LocationContext);
    const map = useRef(null);
    const camera = useRef(null);
    const [isMapReady, setIsMapReady] = useState(false);
    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
    // Add this flag to track if initial centering has been done
    const [initialCenteringDone, setInitialCenteringDone] = useState(false);
    // Add this state to track if we've received a valid location
    const [hasValidLocation, setHasValidLocation] = useState(false);

    // Add default coordinates for US center as fallback
    const DEFAULT_COORDINATES: [number, number] = [-95.7129, 37.0902]; // US center

    // Initialize map with platform-specific handling
    useEffect(() => {
        MapboxGL.setTelemetryEnabled(false);

        // Set initial center to default coordinates
        setCenterCoordinates(DEFAULT_COORDINATES);
    }, []);

    // Handle Android permissions
    const checkAndroidPerms = async () => {
        try {
            const permission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
            if (!permission) {
                await getAndroidPermission();
            } else {
                setLocationPermissionGranted(permission);
            }
        } catch (err) {
            console.warn('Error checking location permission:', err);
        }
    };

    const getAndroidPermission = async () => {
        try {
            const permission = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
            setLocationPermissionGranted(permission === 'granted');
        } catch (err) {
            console.warn('Error requesting location permission:', err);
        }
    };

    // Handle permissions based on platform
    useEffect(() => {
        if (Platform.OS === 'ios') {
            setLocationPermissionGranted(true);
        } else {
            checkAndroidPerms();
        }
    }, []);

    // Handle location updates and validate
    useEffect(() => {
        if (userLocation?.latitude && userLocation?.longitude) {
            const isValidLocation =
                userLocation.latitude !== 0 &&
                userLocation.longitude !== 0 &&
                !isNaN(userLocation.latitude) &&
                !isNaN(userLocation.longitude) &&
                // Add additional validation to prevent ocean coordinates
                Math.abs(userLocation.latitude) <= 90 &&
                Math.abs(userLocation.longitude) <= 180;

            if (isValidLocation) {
                console.log('Valid user location received:', userLocation);
                setHasValidLocation(true);
            } else {
                console.warn('Invalid location received:', userLocation);
                // If invalid location, keep using default coordinates
                setHasValidLocation(false);
            }
        }
    }, [userLocation]);

    // Set initial camera position when map is ready and we have a valid location
    useEffect(() => {
        if (isMapReady && hasValidLocation && userLocation) {
            try {
                // Use a timeout to ensure the map is fully loaded
                setTimeout(() => {
                    // For both platforms, set center coordinates first
                    setCenterCoordinates([userLocation.longitude, userLocation.latitude]);

                    // Then update the camera
                    if (Platform.OS === 'android') {
                        if (camera.current) {
                            //@ts-ignore
                            camera.current.setCamera({
                                centerCoordinate: [userLocation.longitude, userLocation.latitude],
                                zoomLevel: 12,
                                animationDuration: 1000,
                            });
                        }
                    } else {
                        if (camera.current) {
                            //@ts-ignore
                            camera.current.setCamera({
                                centerCoordinate: [userLocation.longitude, userLocation.latitude],
                                zoomLevel: 12,
                                animationDuration: 1500,
                            });
                        }
                    }
                }, 500);
            } catch (error) {
                console.error('Error setting camera position:', error);
                // If there's an error, use default coordinates
                setCenterCoordinates(DEFAULT_COORDINATES);
            }
        }
    }, [isMapReady, hasValidLocation, userLocation]);

    const onMarkerPressed = (event: any) => {
        try {
            // Get the feature and its coordinates
            const feature = event.features[0];
            if (!feature) return;

            // Extract coordinates from the feature's geometry
            const coordinates = feature.geometry.coordinates;
            if (!coordinates || coordinates.length < 2) return;
            actions.setMapCurrentClub(feature as unknown as MapClub);

            // First update the selected club state
            if (feature.id === selectedClub?.id) {
                // setSelectedClub(null);
                // setIsMarkerClicked(false);
            } else {
                setSelectedClub(feature);
                setIsMarkerClicked(true);

                // Platform-specific handling for marker selection
                if (Platform.OS === 'ios') {
                    // For iOS, directly set coordinates and use a longer timeout
                    setCenterCoordinates([coordinates[0], coordinates[1]]);

                    // Use a longer timeout to ensure the map is ready
                    setTimeout(() => {
                        if (camera.current) {
                            console.log('iOS: Moving camera in onMarkerPressed to:', coordinates[0], coordinates[1]);

                            // Direct camera manipulation for iOS
                            //@ts-ignore
                            camera.current.setCamera({
                                centerCoordinate: [coordinates[0], coordinates[1]],
                                zoomLevel: 12,
                                animationDuration: 1500,
                            });
                        }
                    }, 500);
                } else {
                    // For Android, use the existing approach
                    setTimeout(() => {
                        changeRegion(coordinates[1], coordinates[0], true);
                    }, 50);
                }
            }
            setTimeout(() => {
                navigation.navigate('MapDetails', {
                    comeFromOffer: true,
                    prevScreenCallBack: () => {
                        navigation.goBack();
                        setActiveView('LIST');
                    },
                });
            }, 1500);
        } catch (error) {
            console.error('Error in onMarkerPressed:', error);
        }
    };

    // Platform-specific implementation of handleCurrentLocation
    const handleCurrentLocation = () => {
        if (!userLocation?.latitude || !userLocation?.longitude) return;

        try {
            // Validate coordinates
            if (
                isNaN(userLocation.latitude) ||
                isNaN(userLocation.longitude) ||
                Math.abs(userLocation.latitude) > 90 ||
                Math.abs(userLocation.longitude) > 180
            ) {
                console.warn('Invalid coordinates in handleCurrentLocation:', userLocation);
                return;
            }

            actions?.setMapCurrentState({
                lat: userLocation.latitude,
                lng: userLocation.longitude,
            });

            if (Platform.OS === 'ios') {
                // iOS-specific implementation
                setCenterCoordinates([userLocation.longitude, userLocation.latitude]);

                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [userLocation.longitude, userLocation.latitude],
                            zoomLevel: 12,
                            animationDuration: 1500,
                        });
                    }
                }, 300);
            } else {
                // Keep Android implementation unchanged
                changeRegion(userLocation.latitude, userLocation.longitude, true);
            }
        } catch (error) {
            console.error('Error in handleCurrentLocation:', error);
        }
    };

    // Platform-specific implementation of changeRegion with error handling
    const changeRegion = async (latitude: number, longitude: number, isZoomNeeded?: boolean) => {
        // Ensure valid coordinates
        if (
            !latitude ||
            !longitude ||
            isNaN(latitude) ||
            isNaN(longitude) ||
            Math.abs(latitude) > 90 ||
            Math.abs(longitude) > 180
        ) {
            console.warn('Invalid coordinates in changeRegion:', latitude, longitude);
            return;
        }

        try {
            console.log('Changing region to:', latitude, longitude, 'Platform:', Platform.OS);

            if (Platform.OS === 'android') {
                // Android-specific implementation
                setCenterCoordinates([longitude, latitude]);

                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [longitude, latitude],
                            zoomLevel: isZoomNeeded ? 12 : zoomLevel,
                            animationDuration: 1000,
                        });
                    }
                }, 200);
            } else {
                // iOS implementation
                setCenterCoordinates([longitude, latitude]);

                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.moveTo([longitude, latitude], 500);
                        if (isZoomNeeded) {
                            setTimeout(() => {
                                //@ts-ignore
                                camera.current.zoomTo(12, 300);
                            }, 200);
                        }
                    }
                }, 200);
            }
        } catch (error) {
            console.error('Error in changeRegion:', error);
            // If there's an error, use default coordinates
            setCenterCoordinates(DEFAULT_COORDINATES);
        }
    };

    useEffect(() => {
        setTimeout(() => {
            if (routeClub && Array.isArray(routeClub.geometry.coordinates)) {
                setSelectedClub(routeClub);
                setSearchedClub(routeClub);
                setIsMarkerClicked(true);
                changeRegion(
                    Number(routeClub.geometry.coordinates[1]),
                    Number(routeClub.geometry.coordinates[0]),
                    true,
                );
                setTimeout(() => {
                    setActiveView('LIST');
                }, 200);
            }
        }, 4000);
    }, [routeClub]);

    useEffect(() => {
        if (selectedRegion) {
            changeRegion(selectedRegion.lat, selectedRegion.lng, true);
        }
    }, [selectedRegion]);

    // Add this useEffect to handle searchedClub changes - completely rewritten for iOS
    useEffect(() => {
        if (!searchedClub || !searchedClub.geometry || !searchedClub.geometry.coordinates) return;

        try {
            const [longitude, latitude] = searchedClub.geometry.coordinates;

            // Ensure we have valid coordinates
            if (!longitude || !latitude || isNaN(Number(longitude)) || isNaN(Number(latitude))) {
                console.warn('Invalid coordinates in searchedClub:', longitude, latitude);
                return;
            }

            console.log('Centering on searched club:', longitude, latitude, 'Platform:', Platform.OS);

            // Force coordinates to be numbers
            const lng = Number(longitude);
            const lat = Number(latitude);

            // Platform-specific direct camera control
            if (Platform.OS === 'ios') {
                // For iOS, use a completely different approach
                // First set the center coordinates
                setCenterCoordinates([lng, lat]);

                // Then use a longer timeout and direct camera manipulation
                setTimeout(() => {
                    if (camera.current) {
                        console.log('iOS: Setting camera position to:', lng, lat);

                        // Use setCamera with a longer animation duration
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [lng, lat],
                            zoomLevel: 12,
                            animationDuration: 2000,
                        });
                    }
                }, 1000);
            } else {
                // For Android, use the existing approach
                setCenterCoordinates([lng, lat]);

                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [lng, lat],
                            zoomLevel: 12,
                            animationDuration: 1000,
                        });
                    }
                }, 300);
            }
            // actions.setMapCurrentClub(searchedClub as unknown as MapClub);
            // setTimeout(() => {
            //     navigation.navigate('MapDetails', { comeFromOffer: true, prevScreenCallBack: () => {
            //         navigation.goBack();
            //         actions.setMapCurrentClub(null);
            //         setActiveView('LIST');
            //     } });
            // }, 2000);
        } catch (error) {
            console.error('Error in searchedClub effect:', error);
        }
    }, [searchedClub, routeClub]);

    // This function called when we change the region on map
    const handleRegionChange = async (data: any) => {
        try {
            //If the coordinates are same then do not change the map state
            if (
                state?.mapCurrentState?.lat === data?.geometry?.coordinates[1] &&
                state?.mapCurrentState?.lng === data?.geometry?.coordinates[0]
            ) {
                return;
            }
            actions?.setMapCurrentState({
                lat: data?.geometry?.coordinates[1],
                lng: data?.geometry?.coordinates[0],
            });
            setMapBoundaries(data?.properties?.visibleBounds);
        } catch (error) {}
    };

    const newClub = useMemo(() => {
        return offersMapMarkers?.map((item: any) => {
            if (item.geometry.coordinates[0] === null && item.geometry.coordinates[1] === null) {
                return {
                    ...item,
                    geometry: {
                        type: 'Point',
                        coordinates: [0, 0],
                    },
                };
            } else {
                return item;
            }
        });
    }, [offersMapMarkers]);

    if (
        locationPermissionGranted &&
        //@ts-ignore
        locationPermissionGranted !== 'never_ask_again'
    ) {
        return (
            <View style={styles.container}>
                <MapboxGL.MapView
                    style={{ flex: 1 }}
                    ref={map}
                    onRegionDidChange={handleRegionChange}
                    attributionEnabled={false}
                    compassEnabled={false}
                    compassFadeWhenNorth={false}
                    scaleBarEnabled={false}
                    rotateEnabled={false}
                    logoEnabled={false}
                    pitchEnabled={false}
                    onDidFinishLoadingMap={() => setIsMapReady(true)}>
                    {isMapReady && (
                        <>
                            <MapboxGL.UserLocation visible={true} />
                            <MapboxGL.Camera
                                ref={camera}
                                minZoomLevel={4}
                                maxZoomLevel={13}
                                animationDuration={Platform.OS === 'ios' ? 1000 : 2000}
                                animationMode="easeTo"
                                zoomLevel={selectedClub ? 12 : 6}
                                centerCoordinate={centerCoordinates}
                            />
                            <MapCluster
                                clubs={newClub.map((club: MapClub) => ({
                                    ...club,
                                    properties: {
                                        ...club.properties,
                                        isSelected: Number(selectedClub?.id) === club.id,
                                    },
                                }))}
                                onItemPress={(event: OnPressEvent) => {
                                    onMarkerPressed(event as unknown as OnPressEvent);
                                }}
                            />
                        </>
                    )}
                </MapboxGL.MapView>
                <UserLocationButton setUserRegion={handleCurrentLocation} />
                <ScreenResizeButton />
                {activeView === 'LIST' && !showFilterOption && (
                    <OfferListingScreen
                        renderedOffers={renderedOffers}
                        dateRange={dateRange}
                        setDateRange={setDateRange}
                        handleRequestAgainstOffer={handleRequestAgainstOffer}
                        handleEditOffer={handleEditOffer}
                        handleDeleteOffer={handleDeleteOffer}
                        handleOfferDetailPress={handleOfferDetailPress}
                    />
                )}
            </View>
        );
    } // @ts-ignore
    if (!locationPermissionGranted || locationPermissionGranted === 'never_ask_again') {
        return (
            <View style={styles.noLocPerms}>
                <View
                    style={{
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <Text style={{ padding: 40, textAlign: 'center' }}>
                        You must enable location permissions in your phone's settings to use the Clubs map.
                    </Text>
                    <Button
                        title="Open Settings"
                        onPress={() => {
                            Linking.openSettings();
                        }}
                    />
                </View>
            </View>
        );
    } else
        return (
            <View style={styles.loader}>
                <ActivityIndicator size="large" color={colors.tealRgb} />
            </View>
        );
};

export default memo(OfferView);

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    mapContainer: {
        flex: 1,
    },
    loader: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noLocPerms: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'white',
        borderRadius: 15,
        zIndex: 0,
    },
});
