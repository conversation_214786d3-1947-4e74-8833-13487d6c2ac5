import { <PERSON>L<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';

// theme, utils, hooks imports
import { colors } from '../../../theme/theme';
import TopNavBar from '../../../components/TopNavBar';
import { Spacing } from '../../../utils/responsiveUI';
import OfferListingCard from './OfferListingCard';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { DateRange, MapClub, Offer } from '../../../interface';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../interface/type';
import { useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../../context/AuthContext';
import moment from 'moment';
import { apiServices } from '../../../service/apiServices';
import RequestEmptyScreen from '../../requests/view/RequestEmptyScreen';
import { NO_ACTIVE_OFFERS, NO_OFFERS_AVAILABLE } from '../../../utils/constants/strings';

const OfferListingScreen = ({
    renderedOffers,
    dateRange,
    setDateRange,
    handleRequestAgainstOffer,
    handleEditOffer,
    handleDeleteOffer,
    handleOfferDetailPress,
}: {
    renderedOffers: MapClub[];
    dateRange: DateRange;
    setDateRange: (dateRange: DateRange) => void;
    handleRequestAgainstOffer: (item: Offer) => void;
    handleEditOffer: (offer: Offer) => void;
    handleDeleteOffer: (offer: Offer) => void;
    handleOfferDetailPress: (item: Offer) => void;
}) => {
    const [activeTab, setActiveTab] = useState<string>('All');
    const { user } = useContext(AuthContext);
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const tab = [
        {
            name: 'All',
        },
        {
            name: 'My Offers',
        },
    ];
    const [offerList, setOfferList] = useState<Offer[]>([]);
    const mapOpacity = useSharedValue(0);

    useEffect(() => {
        setTimeout(() => {
            mapOpacity.value = withTiming(1, { duration: 200 });
        }, 200);
    }, []);
    const mapAnimatedStyle = useAnimatedStyle(() => ({
        opacity: mapOpacity.value,
    }));

    useEffect(() => {
        setOfferList([]);
        {
            renderedOffers?.map((club: MapClub) => {
                // Ensure offers exists with a default empty array
                const clubOffers = club?.properties?.offers || [];

                // Filter based on active tab
                let filteredOffers =
                    activeTab === 'All' ? clubOffers : clubOffers.filter((offer: Offer) => offer.user_id === user?.id);

                // Apply date filters if present
                if (dateRange.startDate || dateRange.endDate) {
                    filteredOffers = filteredOffers.filter((offer: Offer) => {
                        const start_date = moment.utc(offer.start_date).format('YYYY-MM-DD');
                        const end_date = moment.utc(offer.end_date).format('YYYY-MM-DD');
                        if (dateRange.startDate && dateRange.endDate) {
                            return (
                                moment(start_date).isBetween(
                                    moment.utc(dateRange.startDate).format('YYYY-MM-DD'),
                                    moment.utc(dateRange.endDate).format('YYYY-MM-DD'),
                                    'day',
                                    '[]',
                                ) ||
                                moment(end_date).isBetween(
                                    moment.utc(dateRange.startDate).format('YYYY-MM-DD'),
                                    moment.utc(dateRange.endDate).format('YYYY-MM-DD'),
                                    'day',
                                    '[]',
                                )
                            );
                        }

                        if (dateRange.startDate) {
                            const searchDate = moment.utc(dateRange.startDate).format('YYYY-MM-DD');
                            return (
                                moment(start_date).isSame(searchDate, 'day') ||
                                (moment(start_date).isBefore(searchDate, 'day') &&
                                    moment(end_date).isAfter(searchDate, 'day'))
                            );
                        }

                        return true;
                    });
                }

                filteredOffers.map((item: Offer, index: number) => {
                    setOfferList((prev) => [...prev, { ...item, club_id: club.id }]);
                });
            });
        }
    }, [activeTab, dateRange, renderedOffers]);

    return (
            <Animated.View style={[styles.container, mapAnimatedStyle]}>
                <TopNavBar tabList={tab} activeTab={activeTab} setActiveTab={setActiveTab} />
                <FlatList
                    data={offerList}
                    renderItem={({ item, index }) => (
                        <OfferListingCard
                            item={item}
                            navigation={navigation}
                            index={index}
                            club={renderedOffers.find((club) => club.id === item.club_id) || null}
                            handleRequestAgainstOffer={handleRequestAgainstOffer}
                            handleEditOffer={handleEditOffer}
                            handleDeleteOffer={handleDeleteOffer}
                            handleOfferDetailPress={handleOfferDetailPress}
                        />
                    )}
                    showsVerticalScrollIndicator={false}
                    keyExtractor={(item) => item.offer_id.toString()}
                    ListEmptyComponent={
                        <RequestEmptyScreen
                            wrapperStyle={{ marginTop: Spacing.SCALE_120 }}
                            text={activeTab === 'My Offers' ? NO_ACTIVE_OFFERS : NO_OFFERS_AVAILABLE}
                        />
                    }
                    contentContainerStyle={{ paddingBottom: Spacing.SCALE_30 }}
                    style={styles.flatListContainer}
                />
            </Animated.View>
    );
};

export default OfferListingScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.screenBG,
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        paddingHorizontal: Spacing.SCALE_16,
    },
    flatListContainer: {
        marginTop: Spacing.SCALE_4,
        // marginBottom: Spacing.SCALE_50,
    },
});
