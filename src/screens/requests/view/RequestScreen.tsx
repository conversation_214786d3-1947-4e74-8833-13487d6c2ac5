import {
    <PERSON><PERSON>,
    RefreshControl,
    ScrollView,
    StyleSheet,
    View,
    Animated,
    TouchableOpacity,
    Platform,
} from 'react-native';
import React, { useContext, useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useFocusEffect } from '@react-navigation/native';

import HomeScreenCommonHeader from '../../../components/homeScreenComponent/HomeScreenCommonHeader';
import matchTokenActive from '../action/handleCanICheckRequest';
import { AuthContext } from '../../../context/AuthContext';
import { RootStackParamList } from '../../../interface/type';
import { RightArrowSvg } from '../../../assets/svg';
import { ACCEPTED, RECEIVED, REQUESTED } from '../../../utils/constants/strings';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import RequestListSection from './RequestListSection';
import { GlobalContext } from '../../../context/contextApi';
import { getAllRequest } from '../action/getAllRequest';
import RequestScreenSkelton from './RequestScreenSkelton';
import AcceptRequestModalNew from '../../../components/modals/AcceptRequestModalNew';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { getStreamRequestChannel } from '../action/getRequestUnReadChannels';
import { getAllRequestHostsData } from '../action/getAllRequestHostsData';
import CustomRM from '../../../components/layout/requests/customRM';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import useClient from '../../../hooks/useClient';
import DateRangeModal1 from '../../../components/modals/DateRangeModal1';
import { getRequests } from '../action/getRequests';
import { GET_UNREAD_MESSAGE_STATUS, receivedAcceptedURL } from '../../../service/EndPoint';
import ReviewModalNew from '../../../components/modals/ReviewModalNew';
import RequestEmptyScreen from './RequestEmptyScreen';
import { getRecommendedClubs } from '../Utils';
import { handleGetAllFriendsId } from '../../my-TG-Stream-Chat/action';
import { fetcher } from '../../../service/fetcher';
import showToast from '../../../components/toast/CustomToast';

interface RequestModalState {
    request: any;
    onRefresh: () => void;
}

interface DateRangeModalState {
    startDate?: Date;
    endDate?: Date;
    [key: string]: any;
}

interface ReviewModalState {
    id?: string;
    status?: string;
    [key: string]: any;
}

interface RequestScreenState {
    isRecommendedClubRequest: boolean;
    showRecommendationPopup: boolean;
    requestedClub: any | null;
    acceptRequestModal: RequestModalState | null;
    isVisible: boolean;
    isAccepted: boolean;
    dateRangeModal: DateRangeModalState | undefined;
    declineRequest: any | null;
    reviewModal: ReviewModalState | undefined;
    isMyRequest: boolean;
}

type RequestScreenProps = NativeStackScreenProps<RootStackParamList, 'RequestScreen'>;

const RequestScreen: React.FC<RequestScreenProps> = ({ navigation }) => {
    const { user } = useContext(AuthContext);
    const { client, setChannel } = useContext(StreamChatContext);
    const apolloClient = useClient();
    const { state, actions } = useContext(GlobalContext);
    const {
        receivedOpen,
        requestedOpen,
        allAcceptedRequests,
        appSkeltonLoader,
        requestedAccepted,
        unreadChannels,
        unReadMessageStatus,
    } = state;

    // Animated scroller button refs and states
    const scrollViewRef = useRef<ScrollView>(null);
    const translateX = useRef(new Animated.Value(0)).current;
    const isScrolling = useRef(false);
    const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
    const [isScrollable, setIsScrollable] = useState(false);
    const [showAcceptRequestPopupInfo, setShowAcceptRequestPopupInfo] = useState<any>(null);
    const contentHeight = useRef(0);
    const scrollViewHeight = useRef(0);
    const currentScrollPosition = useRef(0);
    const isAtBottom = useRef(false);

    const [screenState, setScreenState] = useState<RequestScreenState>({
        isRecommendedClubRequest: false,
        showRecommendationPopup: false,
        requestedClub: null,
        acceptRequestModal: null,
        isVisible: false,
        isAccepted: false,
        dateRangeModal: undefined,
        declineRequest: null,
        reviewModal: undefined,
        isMyRequest: false,
    });

    //Fetch unread message red dot value for all tabs
    const fetchUnreadMessage = () => {
        fetcher({
            endpoint: GET_UNREAD_MESSAGE_STATUS,
            method: 'POST',
            body: {
                userId: user?.id,
                requestIds: unreadChannels,
            },
        }).then((res) => {
            if (res?.status) {
                actions?.setUnreadMessageStatus(res?.response);
            } else {
                showToast({});
            }
        });
    };

    // Add scroll handling functions
    const showMessageIcon = useCallback(() => {
        if (!isScrollable) return;
        Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
            tension: 50,
            friction: 7,
        }).start();
    }, [isScrollable]);

    const hideMessageIcon = useCallback(() => {
        Animated.spring(translateX, {
            toValue: 100,
            useNativeDriver: true,
            tension: 50,
            friction: 7,
        }).start();
    }, []);

    const checkIfScrollable = useCallback(
        (contentWidth: number, contentHeight: number) => {
            if (scrollViewHeight.current === 0) return;

            const isContentScrollable = contentHeight > scrollViewHeight.current;
            if (isContentScrollable !== isScrollable) {
                setIsScrollable(isContentScrollable);
                translateX.setValue(isContentScrollable ? 0 : 100);
            }
        },
        [isScrollable],
    );

    const checkIfAtBottom = useCallback((currentOffset: number, contentSize: number, layoutSize: number) => {
        if (contentSize <= 0 || layoutSize <= 0) return false;
        const maxScroll = contentSize - layoutSize;
        return currentOffset >= maxScroll - 1; // Using 1px threshold for more precise detection
    }, []);

    // Check scrollability when data changes
    useEffect(() => {
        fetchUnreadMessage();
        handleGetAllFriendsId(user?.id, actions);
        // setChannel({});
        if (receivedOpen.length || requestedOpen.length || allAcceptedRequests.length) {
            // Force check scrollability after data changes
            if (contentHeight.current && scrollViewHeight.current) {
                checkIfScrollable(0, contentHeight.current);
            }
        } else {
            setIsScrollable(false);
            translateX.setValue(100);
        }
    }, [receivedOpen, requestedOpen, allAcceptedRequests, checkIfScrollable]);

    const scrollToNextSection = useCallback(() => {
        if (!scrollViewRef.current) return;

        // Calculate next scroll position (current position + screen height)
        const nextPosition = currentScrollPosition.current + scrollViewHeight.current;

        // Don't scroll past the end
        const maxScroll = Math.max(0, contentHeight.current - scrollViewHeight.current);
        const targetPosition = Math.min(nextPosition, maxScroll);

        // Check if this scroll will take us to the bottom
        if (targetPosition >= maxScroll) {
            isAtBottom.current = true;
            hideMessageIcon();
        }

        scrollViewRef.current.scrollTo({
            y: targetPosition,
            animated: true,
        });

        // Update current scroll position
        currentScrollPosition.current = targetPosition;
    }, [hideMessageIcon]);

    const handleScroll = useCallback(
        (event: any) => {
            if (!isScrollable) return;

            const currentOffset = event.nativeEvent.contentOffset.y;
            const contentSize = event.nativeEvent.contentSize.height;
            const layoutSize = event.nativeEvent.layoutMeasurement.height;

            // Store current scroll position
            currentScrollPosition.current = currentOffset;

            // Check if we're at the bottom
            const atBottom = checkIfAtBottom(currentOffset, contentSize, layoutSize);

            // Update bottom state and handle icon visibility
            if (atBottom !== isAtBottom.current) {
                isAtBottom.current = atBottom;
                if (atBottom) {
                    hideMessageIcon();
                } else {
                    showMessageIcon();
                }
                return;
            }

            // Handle normal scrolling behavior
            if (!isScrolling.current) {
                isScrolling.current = true;
                if (!isAtBottom.current) {
                    hideMessageIcon();
                }
            }

            if (scrollTimeout.current) {
                clearTimeout(scrollTimeout.current);
            }

            scrollTimeout.current = setTimeout(() => {
                isScrolling.current = false;
                if (!isAtBottom.current) {
                    showMessageIcon();
                }
            }, 50);
        },
        [isScrollable, hideMessageIcon, showMessageIcon, checkIfAtBottom],
    );

    // Reset bottom state when content changes
    useEffect(() => {
        isAtBottom.current = false;
    }, [receivedOpen, requestedOpen, allAcceptedRequests]);

    const refreshData = useCallback(() => {
        getAllRequest(actions, user?.id);
        getStreamRequestChannel(client, user, actions, state);
        fetchUnreadMessage();
    }, [actions, user?.id, client]);

    // Consolidate focus and initial load effects
    useFocusEffect(
        useCallback(() => {
            refreshData();
        }, [refreshData]),
    );

    // Handle request hosts data
    useEffect(() => {
        if (requestedOpen.length > 0 || requestedAccepted.length > 0) {
            const requestIds = [...requestedOpen, ...requestedAccepted].map((request: any) => request.request_id);
            getAllRequestHostsData({
                userId: user?.id,
                requestIds: requestIds,
            }).then((res) => {
                if (res.status) {
                    actions.setAllRequestHostsData(res.data);
                }
            });
        }
    }, [requestedOpen, requestedAccepted, user?.id, actions]);

    // Stream chat message listener
    useEffect(() => {
        const messageNewEventListener = client.on('message.new', () => {
            getStreamRequestChannel(client, user, actions, state);
            fetchUnreadMessage();
        });
        return () => messageNewEventListener?.unsubscribe();
    }, []);

    const handleModalClose = useCallback(() => {
        setScreenState((prev) => ({ ...prev, isVisible: false }));
    }, []);

    const handleContinue = useCallback(() => {
        setScreenState((prev) => ({ ...prev, isVisible: false }));

        if (screenState.isAccepted) {
            apolloClient.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: false,
                        showCreateRequestPopup: user?.additional_settings?.showCreateRequestPopup,
                    },
                },
            });
        }

        setTimeout(() => {
            navigation.navigate('RequestConfirmScreen', showAcceptRequestPopupInfo);
        }, 100);
    }, [screenState.isAccepted, apolloClient, user?.id, showAcceptRequestPopupInfo]);

    const handleCreateRequest = useCallback(
        async (arg?: any) => {
            const res = await matchTokenActive(user?.id);
            if (!res?.canCreate) {
                navigation.navigate('DeleteChannelConfirmationPopup', {
                    popupSubText: res?.message,
                    firstBtnLabel: 'Cancel',
                    secondBtnLabel: 'Ok',
                });
                return;
            }

            if (arg?.isRecommendedClubRequest) {
                setScreenState((prev) => ({ ...prev, isRecommendedClubRequest: false }));
                // @ts-ignore
                navigation.navigate('Create Request', { club: { clubs: arg?.club } });
            } else {
                navigation.navigate('Create Request', {
                    onGoBack: (data) => {
                        setScreenState((prev) => ({ ...prev, requestedClub: data }));
                    },
                });
            }
        },
        [navigation, user?.id],
    );

    useEffect(() => {
        if (screenState.requestedClub?.id) {
            const getClub = async () => {
                const res = await getRecommendedClubs({ userId: user?.id, clubId: screenState.requestedClub?.id });
                if (res?.status === 1) {
                    if (res?.data?.length) {
                        setScreenState((prev) => ({ ...prev, recommendedClubs: res?.data }));
                        navigation.navigate('Recommended Club', {
                            recommendedClubs: res?.data,
                            requestedClub: screenState.requestedClub,
                        });
                    }
                }
            };

            getClub();
        }
    }, [screenState.requestedClub]);

    const handleRequestHistory = useCallback(() => {
        navigation.navigate('RequestHistoryScreen');
    }, [navigation]);

    const hasNoRequests = useMemo(
        () => !receivedOpen.length && !requestedOpen.length && !allAcceptedRequests.length,
        [receivedOpen.length, requestedOpen.length, allAcceptedRequests.length],
    );

    const renderContent = () => (
        <ScrollView
            ref={scrollViewRef}
            style={styles.body}
            showsVerticalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={32}
            onContentSizeChange={(w, h) => {
                contentHeight.current = h;
                checkIfScrollable(w, h);
            }}
            onLayout={(event) => {
                const { height } = event.nativeEvent.layout;
                scrollViewHeight.current = height;
                if (contentHeight.current) {
                    checkIfScrollable(0, contentHeight.current);
                }
            }}
            refreshControl={<RefreshControl refreshing={appSkeltonLoader} onRefresh={refreshData} />}>
            <RequestListSection
                title="Received Open"
                subtitle="Accept or Decline received requests"
                data={receivedOpen}
                setAcceptRequestModal={(modal: RequestModalState | null) =>
                    setScreenState((prev) => ({ ...prev, acceptRequestModal: modal }))
                }
                acceptRequestModal={screenState.acceptRequestModal}
                type={RECEIVED}
                setIsVisible={(visible) => setScreenState((prev) => ({ ...prev, isVisible: visible }))}
                isVisible={screenState.isVisible}
                isAccepted={screenState.isAccepted}
                setIsAccepted={(accepted) => setScreenState((prev) => ({ ...prev, isAccepted: accepted }))}
                setIsMyRequest={(isMyRequest) => setScreenState((prev) => ({ ...prev, isMyRequest }))}
                isMyRequest={screenState.isMyRequest}
                marginBottom={
                    receivedOpen.length > 0 && requestedOpen.length === 0 && allAcceptedRequests.length === 0
                        ? true
                        : false
                }
                setShowAcceptRequestPopupInfo={setShowAcceptRequestPopupInfo}
            />
            <RequestListSection
                title="Awaiting Acceptance"
                subtitle="Your sent requests awaiting acceptance"
                data={requestedOpen}
                type={REQUESTED}
                setIsMyRequest={(isMyRequest) => setScreenState((prev) => ({ ...prev, isMyRequest }))}
                isMyRequest={screenState.isMyRequest}
                marginBottom={allAcceptedRequests.length === 0 ? true : false}
            />
            <RequestListSection
                title="Accepted Request"
                subtitle="Both requested and received requests"
                data={allAcceptedRequests}
                type={ACCEPTED}
                setDateRangeModal={(modal: DateRangeModalState | undefined) =>
                    setScreenState((prev) => ({ ...prev, dateRangeModal: modal }))
                }
                dateRangeModal={screenState.dateRangeModal}
                setReviewModal={(modal: ReviewModalState | undefined) =>
                    setScreenState((prev) => ({ ...prev, reviewModal: modal }))
                }
                reviewModal={screenState.reviewModal}
                setDeclineRequest={(request) => setScreenState((prev) => ({ ...prev, declineRequest: request }))}
                declineRequest={screenState.declineRequest}
                setIsMyRequest={(isMyRequest) => setScreenState((prev) => ({ ...prev, isMyRequest }))}
                isMyRequest={screenState.isMyRequest}
                marginBottom={allAcceptedRequests.length > 0 ? true : false}
            />
        </ScrollView>
    );

    return (
        <>
            <View style={styles.container}>
                <HomeScreenCommonHeader
                    title="Requests"
                    showCreateRequestIcon={true}
                    handleCreateRequest={handleCreateRequest}
                    handleRequestHistory={handleRequestHistory}
                    showRequestHistory={true}
                    historyUnreadMessageStatus={
                        unReadMessageStatus['historyReceived'] || unReadMessageStatus['historyRequested']
                    }
                />
                {appSkeltonLoader ? <RequestScreenSkelton /> : hasNoRequests ? <RequestEmptyScreen /> : renderContent()}
            </View>

            {screenState.acceptRequestModal && (
                <AcceptRequestModalNew
                    modal={screenState.acceptRequestModal}
                    setModal={(modal: RequestModalState | null) =>
                        setScreenState((prev) => ({ ...prev, acceptRequestModal: modal }))
                    }
                />
            )}

            {user?.additional_settings?.showAcceptRequestPopup && (
                <CustomRM
                    isVisible={screenState.isVisible}
                    label={'Request'}
                    heading="Are you sure you want to accept the request?"
                    data={[
                        `This is a no-obligation system.  You don't have to accept anything that doesn't work for you`,
                        `Clarify that logistics work before accepting`,
                        `Learn enough about the requester so that you are sure you want to spend a few hours with them`,
                        `We are not big fans of "unaccompanied" play.  Accept if you can host.  If setting up unaccompanied play, remember that you are responsible for guest conduct`,
                    ]}
                    isChecked={screenState.isAccepted}
                    handleChecked={() => setScreenState((prev) => ({ ...prev, isAccepted: !prev.isAccepted }))}
                    handleContinue={handleContinue}
                    handleCancel={handleModalClose}
                    handleBack={handleModalClose}
                />
            )}

            {screenState.dateRangeModal && (
                <View style={styles.dateRangeModalContainer}>
                    <DateRangeModal1
                        modal={screenState.dateRangeModal}
                        setModal={(modal: DateRangeModalState | undefined) =>
                            setScreenState((prev) => ({ ...prev, dateRangeModal: modal }))
                        }
                        getRequests={getRequests}
                        receivedAcceptedURL={receivedAcceptedURL}
                        user_id={user.id}
                        setReceivedAccepted={() => {}}
                        getAllRequest={() => getAllRequest(actions, user?.id)}
                    />
                </View>
            )}

            {screenState.reviewModal && (
                <ReviewModalNew
                    request={screenState.declineRequest}
                    isMyRequest={screenState.isMyRequest}
                    closeModal={() => {
                        setScreenState((prev) => ({ ...prev, reviewModal: undefined }));
                        getAllRequest(actions, user?.id);
                    }}
                />
            )}
            <Animated.View
                style={[
                    styles.messageContainer,
                    {
                        transform: [{ translateX: translateX }],
                        opacity: isScrollable ? 1 : 0,
                        pointerEvents: isScrollable ? 'auto' : 'none',
                    },
                ]}>
                <TouchableOpacity
                    style={{
                        height: 32,
                        width: 32,
                        backgroundColor: colors.white,
                        borderRadius: 16,
                        alignItems: 'center',
                        justifyContent: 'center',
                        ...Platform.select({
                            ios: {
                                shadowColor: colors.shadowColor,
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.25,
                                shadowRadius: 4,
                            },
                            android: {
                                elevation: 2,
                                shadowOffset: {
                                    width: 1,
                                    height: 2,
                                },
                                shadowColor: colors.shadowColor,
                                shadowRadius: 4,
                                shadowOpacity: 0.5,
                            },
                        }),
                    }}
                    onPress={scrollToNextSection}
                    activeOpacity={0.8}>
                    <RightArrowSvg style={{ transform: [{ rotate: '90deg' }] }} />
                </TouchableOpacity>
            </Animated.View>
        </>
    );
};

export default React.memo(RequestScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    centerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyStateContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: Size.SIZE_20,
    },
    emptyStateText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_21,
        marginTop: Size.SIZE_24,
    },
    body: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        backgroundColor: colors.screenBG,
    },
    dateRangeModalContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        paddingHorizontal: 30,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
    },
    messageContainer: {
        position: 'absolute',
        top: '90%',
        right: Spacing.SCALE_1,
        padding: Spacing.SCALE_8,
        borderRadius: Size.SIZE_38,
        ...Platform.select({
            ios: {
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
            },
        }),
        zIndex: 999,
    },
});
