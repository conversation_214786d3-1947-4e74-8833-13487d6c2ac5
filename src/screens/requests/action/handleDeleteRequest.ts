import showToast from '../../../components/toast/CustomToast';
import { DELETE_REQUESTED_REQUEST } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';

export const handleDeleteRequestedRequest = (body: any, navigation: any, callBack: () => void) => {
    try {
        return fetcher({
            endpoint: DELETE_REQUESTED_REQUEST,
            method: 'POST',
            body,
        }).then((res) => {
            if (res?.status) {
                navigation.goBack();
                callBack();
            } else {
                showToast({});
            }
        });
    } catch (error) {
        showToast({});
    }
};
