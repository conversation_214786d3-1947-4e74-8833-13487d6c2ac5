import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Linking } from 'react-native';

// Importing SVG icons
import { FaceBookIcon, GreyArrowIcon, LinkedInIcon } from '../../../assets/svg/index';
import { Back } from '../../../assets/images/svg';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { AuthContext } from '../../../context/AuthContext';
import { SocialButtonProps } from '../../../interface';

const SocialButton: React.FC<SocialButtonProps> = ({ Icon, text, fullWidth, link }) => {
    return (
        <TouchableOpacity
            style={[styles.button, fullWidth && styles.fullWidth]}
            onPress={() => {
                link ? Linking.openURL(link) : null;
            }}>
            <View style={styles.content}>
                <Icon width={24} height={24} />
                <Text style={styles.text}>{text}</Text>
            </View>
            <GreyArrowIcon width={Size.SIZE_18} height={Size.SIZE_18} />
        </TouchableOpacity>
    );
};

const SocialLinkComponent: React.FC = () => {
    const { user } = useContext(AuthContext);
    const isSingleSocial = user?.facebook && user?.linkedin ? false : true;
    return (
        <>
            {(user?.facebook || user?.linkedin) && (
                <Text style={styles.socialLinkText}>{isSingleSocial ? 'Social Link' : 'Social Links'}</Text>
            )}
            <View style={styles.container}>
                {user?.facebook && (
                    <SocialButton
                        Icon={FaceBookIcon}
                        text="Facebook"
                        fullWidth={isSingleSocial}
                        link={user?.facebook}
                    />
                )}
                {user?.linkedin && (
                    <SocialButton
                        Icon={LinkedInIcon}
                        text="LinkedIn"
                        fullWidth={isSingleSocial}
                        link={user?.linkedin}
                    />
                )}
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    singleButtonContainer: {
        justifyContent: 'center',
    },
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: Size.SIZE_10,
        padding: Spacing.SCALE_8,
        justifyContent: 'space-between',
        borderColor: colors.lineDividerColor,
        width: '49%', // Default width when there are two buttons
    },
    fullWidth: {
        width: '100%', // Takes full width when only one button is present
    },
    content: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    text: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_4,
    },
    socialLinkText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_12,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_10,
    },
});

export default SocialLinkComponent;
