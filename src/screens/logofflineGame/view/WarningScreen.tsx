import { Linking, StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

// Custom Imports
import { RootStackParamList } from '../../../interface/type';
import { CancelButton } from '../../../components/buttons';
import { WarningIcon } from '../../../assets/svg';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { GlobalContext } from '../../../context/contextApi';

//utils imports
import { OFFLINE_GAME_LOG_WARNING_STRING } from '../../../utils/constants/strings';
import { Size, Typography, Spacing } from '../../../utils/responsiveUI';

//theme imports
import { colors } from '../../../theme/theme';

const WarningScreen = ({ navigation }: NativeStackScreenProps<RootStackParamList>) => {
    const { actions } = useContext(GlobalContext);
    const closeWarningPopup = () => {
        actions.setOfflineLogGameStep(0);
    };

    return (
        <View style={styles.warningPopupContainer}>
            <View
                style={{
                    padding: Spacing.SCALE_10,
                    borderRadius: Size.SIZE_8,
                    backgroundColor: colors.whiteRGB,
                }}>
                <View style={{ marginTop: Spacing.SCALE_10, alignItems: 'center', justifyContent: 'center' }}>
                    <WarningIcon />
                </View>
                <Text style={styles.warningHeader}>Warning!</Text>
                <Text style={styles.warningBody}>{OFFLINE_GAME_LOG_WARNING_STRING}</Text>
                <Text
                    style={[styles.warningBody, { color: colors.tealRgb, marginTop: 0 }]}
                    onPress={() => Linking.openURL(`mailto:<EMAIL>`)}>
                    <EMAIL>
                </Text>
                <View
                    style={{
                        flexDirection: 'row',
                        marginTop: Spacing.SCALE_30,
                        justifyContent: 'space-between',
                    }}>
                    <CancelButton
                        customStyle={styles.buttonStyle}
                        text="Dismiss"
                        textStyle={styles.btnText}
                        onPress={closeWarningPopup}
                    />
                    <TealButtonNew
                        loading={false}
                        btnStyle={[styles.buttonStyle, { backgroundColor: colors.tealRgb }]}
                        text={'I Understand, Continue'}
                        textStyle={styles.btnText}
                        onPress={() => {
                            actions.setOfflineLogGameStep(2);
                        }}
                    />
                </View>
            </View>
        </View>
    );
};

export default WarningScreen;

const styles = StyleSheet.create({
    warningPopupContainer: {
        flex: 1,
        backgroundColor: colors.transparentRgba,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        padding: Spacing.SCALE_16,
        alignItems: 'center',
        justifyContent: 'center',
    },
    warningHeader: {
        fontSize: Typography.FONT_SIZE_24,
        lineHeight: Size.SIZE_32,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
        marginTop: Spacing.SCALE_10,
        color: colors.lightBlack,
    },
    warningBody: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_21,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        textAlign: 'center',
        marginTop: Spacing.SCALE_12,
        color: colors.systemMessageText,
        paddingHorizontal: Spacing.SCALE_20,
    },
    buttonStyle: {
        backgroundColor: colors.lightgray,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_8,
        width: Size.SIZE_150,
    },
    btnText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
    },
});
