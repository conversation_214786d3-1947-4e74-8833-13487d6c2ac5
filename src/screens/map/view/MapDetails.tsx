import { StyleSheet, View, Dimensions, Platform, ScrollView, TouchableOpacity, StatusBar, Alert } from 'react-native';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import MapboxGL from '@rnmapbox/maps';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

import { colors } from '../../../theme/theme';
import MapCluster from '../../../components/map/MapCluster';
import HomeScreenCommonHeader from '../../../components/homeScreenComponent/HomeScreenCommonHeader';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { Back } from '../../../assets/images/svg';
import { RootStackParamList } from '../../../interface/type';
import MapDetailCard from './MapDetailCard';
import { getMapClubDetail } from '../action/getMapClubDetails';
import { AuthContext } from '../../../context/AuthContext';
import { Club, MapClub, MapClubDetail, MapClubOffers, OfferDetail } from '../../../interface';
import { GlobalContext } from '../../../context/contextApi';
import MapButton from '../../../components/map/MapButton';
import { canCreateOfferURL, DELETE_OFFER, tokenCheckURL } from '../../../service/EndPoint';
import useMapState from '../../../hooks/useMapState';
import { fetcher } from '../../../service/fetcher';
import showToast from '../../../components/toast/CustomToast';
import { ERROR } from '../../../utils/constants/strings';
import { CreateRequestNewIconTeal, CreateOfferIconTeal } from '../../../assets/svg';

import { getClubMembersForUser } from '../../../utils/helpers/CreateClubHelper';
import useClient from '../../../hooks/useClient';
import { FAVORITE_CLUB, UNFAVORITE_CLUB } from '../../../graphql/mutations/clubs';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import { getMapClubOffers } from '../action/getMapClubOffers';
import CustomRM from '../../../components/layout/requests/customRM';
import { getGameReview } from '../action/getGameReview';
import getIsClubUnmute from '../../../components/map/actions/getIsClubUnMute';
import constants, { tiers } from '../../../utils/constants/constants';
import { RouteProp } from '@react-navigation/native';

//CleverTap import
const CleverTap = require('clevertap-react-native');

interface MapDetailsProps {
    navigation: NativeStackNavigationProp<RootStackParamList>;
    route: RouteProp<RootStackParamList, 'MapDetails'>;
}

const MapDetails = ({ navigation, route }: MapDetailsProps) => {
    console.log('object', route?.params);
    const { user, refreshUser } = useContext(AuthContext);
    const client = useClient();
    const { setSelectedClub, filter, setMapClubs, clubs, handleBoundriesChange } = useMapState({ user });
    const { actions, state } = useContext(GlobalContext);
    const { mapCurrentClub: club } = state;
    const { height } = Dimensions.get('window');
    const [clubDetail, setClubDetail] = useState<MapClubDetail | null>(null);
    const [memberCount, setMemberCount] = useState(0);
    const [ownClub, setOwnClub] = useState<Club[]>([]);
    const [clubOffers, setClubOffers] = useState<MapClubOffers[]>([]);
    const [showRM, setShowRM] = useState(false);
    const [isAccepted, setIsAccepted] = useState(false);
    const [selectedOffer, setSelectedOffer] = useState<MapClubOffers | null>(null);
    const [gameReviewCount, setGameReviewCount] = useState(0);
    const animatedOpacity = useSharedValue(0);

    const animatedOpacityStyle = useAnimatedStyle(() => ({
        opacity: animatedOpacity.value,
    }));

    const isShowMapLarge = useMemo(() => {
        return !(clubDetail?.tgGroupMembersCount || clubDetail?.friendsPlayed || clubDetail?.friends) &&
            !clubOffers?.length
            ? true
            : false;
    }, [clubDetail, clubOffers]);

    useEffect(() => {
        handleDetailScreenData();
        findClubMember(club?.id);
        handleGameReview();
    }, []);

    useEffect(() => {
        let temporaryClub: Club[] = [];
        user?.clubs?.map((club: any) => {
            temporaryClub.push(club?.club_id);
        });
        setOwnClub(temporaryClub);
    }, [user]);

    const handleClubDetail = async () => {
        const clubDetail = await getMapClubDetail(club.id, club.properties?.color, user);
        const { lat, lng } = clubDetail?.clubs;
        if (lat && lng) {
            setTimeout(() => {
                animatedOpacity.value = withTiming(1, { duration: 300 });
            }, 300); // Add delay before starting animation
            setClubDetail(clubDetail);
        }
    };

    const handleClubOffers = async () => {
        const offers = await getMapClubOffers({
            userId: user?.id,
            clubId: club.id,
            page: 0,
            limit: 10,
        });
        if (offers.status) {
            setClubOffers(offers.data.offers);
        }
    };

    const handleGameReview = async () => {
        const review = await getGameReview({
            userId: user?.id,
            clubId: club.id,
            page: 0,
            limit: 10,
        });
        if (review.status) {
            setGameReviewCount(review.data.pagination.total);
            actions.setGameReview(review.data.reviews);
        }
    };

    const handleDetailScreenData = async () => {
        try {
            actions.setAppLoader(true);
            await Promise.allSettled([handleClubDetail(), handleClubOffers()]);
        } catch (error) {
            console.log('error', error);
        } finally {
            actions.setAppLoader(false);
        }
    };
    // Get the coordinates of the first club in the features array
    const centerCoordinates = useMemo(() => {
        if (club && club?.geometry?.coordinates) {
            return club.geometry.coordinates;
        }
        return [0, 0]; // Default coordinates if none available
    }, [club]);

    async function findClubMember(club_id: number) {
        //@ts-ignore
        const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee
            }
        }`);
        const { tier, visibleToPublic, clubs } = user;
        const user_data = {
            tier,
            visibleToPublic,
            clubs,
        };
        if (view_club_search.length) {
            let extraClubInfo = view_club_search[0];
            const members = getClubMembersForUser({
                user_data,
                user_array: extraClubInfo.user_array,
                filters: {
                    all_ages: true,
                    englishFluency: ['native', 'fluent', 'understandable', 'basic'],
                    gender: 'both',
                    handicap: ['< 5', '5-10', '> 10'],
                    //@ts-ignore
                    max_age: 100,
                    //@ts-ignore
                    min_age: 0,
                    pace: ['fast', 'average', 'leisure'],
                    playAsCouple: false,
                },
                //@ts-ignore
                club_id,
                clubTier: club?.lowest_visible_tier,
            });
            setMemberCount(members.length);
        } else {
            setMemberCount(0);
        }
    }

    const matchTokenActive = ({
        isRequestAgainstOffer = false,
        offer = null,
    }: {
        isRequestAgainstOffer?: boolean;
        offer?: MapClubOffers | null;
    }) => {
        actions.setAppLoader(true);
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body: {
                user_id: user?.id,
                isRequestToAll: !state.allFriendsId[offer?.user_id || ''],
            },
        })
            .then((res) => {
                if (res?.canCreate) {
                    if (isRequestAgainstOffer && offer) {
                        CleverTap.recordEvent(constants.CLEVERTAP.REQUEST_AGAINST_OFFERS, {
                            'Offer ID': offer.offer_id,
                            'Offer created for': offer?.my_tg_group_id?.length ? 'My TG' : 'All',
                            'Locator colour': club?.properties?.color,
                            'Club name': clubDetail?.clubs.name,
                            'Club Tier': clubDetail?.clubs.lowest_visible_tier,
                            'Club Type': clubDetail?.clubs.club_type,
                            'Club Address': clubDetail?.clubs.address,
                            'User email': user.email,
                            'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
                            'User Membership': user.membership_plan?.name, // Active / Inactive
                            'User City': user.stripe_customer_info.address.city,
                            'User State': user.stripe_customer_info.address.state,
                            'User Country': user.stripe_customer_info.address.country,
                            'Club in Current Location or not': 'No', // Yes / No
                        });
                        checkRequest(offer);
                    } else {
                        CleverTap.recordEvent(constants.CLEVERTAP.CREATE_REQUEST_FROM_MAP, {
                            'Club name': clubDetail?.clubs.name,
                            'Club Tier': clubDetail?.clubs.lowest_visible_tier,
                            'Club City': clubDetail?.clubs.address,
                            'Club State': clubDetail?.clubs.address,
                            'Club Country': clubDetail?.clubs.address,
                            'User email': user.email,
                            'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
                            'User City': user.stripe_customer_info.address.city,
                            'User State': user.stripe_customer_info.address.state,
                            'User Country': user.stripe_customer_info.address.country,
                            'User Membership': user.membership_plan?.name, // Active / Inactive
                            'User tag': tiers[user?.tier],
                        });
                        actions?.setSelectedClub(club);
                        navigation.navigate('Create Request', {
                            onGoBack: (data) => {},
                            club: clubDetail,
                            category: filter.category,
                            memberCount,
                            setSelectedClubState: setSelectedClub,
                        });
                    }
                } else {
                    navigation.navigate('DeleteChannelConfirmationPopup', {
                        popupSubText: res?.message,
                        firstBtnLabel: 'Cancel',
                        secondBtnLabel: 'Ok',
                    });
                }
                actions.setAppLoader(false);
            })
            .catch((error) => {
                actions.setAppLoader(false);
            });
    };

    const checkRequest = (offer: MapClubOffers | null) => {
        if (user?.additional_settings?.showCreateRequestPopup) {
            setShowRM(true);
        } else {
            CleverTap.recordEvent(constants.CLEVERTAP.CLICK_ON_CREATE_REQUEST_FROM_MAP, {
                'Club name': clubDetail?.clubs.name,
                'Club Tier': clubDetail?.clubs.lowest_visible_tier,
                'Club Address': clubDetail?.clubs.address,
                'User email': user.email,
                'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
                'User Membership': user.membership_plan?.name, // Active / Inactive
                'User City': user.stripe_customer_info.address.city,
                'User State': user.stripe_customer_info.address.state,
                'User Country': user.stripe_customer_info.address.country,
                'User membership tag': user.membership_plan?.name,
                'User tag': tiers[user?.tier], // Yes / No
            });
            navigation.navigate('Request Against Offer', {
                offer: offer,
                callBack: handleDetailScreenData,
            });
        }
    };

    const isShowCreateRequest = () => {
        if (club?.properties?.color?.includes('blue')) {
            return !!club?.isEligibleForCreateRequest;
        } else if (!['grey', 'grey_contact', 'teal', 'teal_contact'].includes(club?.properties?.color)) {
            return true;
        }
        return false;
    };

    /**
     * Handle favorite the club
     * @param active
     */
    async function handleFavoriteClub(active: boolean) {
        actions.setAppLoader(true);
        let res = await client.request(active ? FAVORITE_CLUB : UNFAVORITE_CLUB, {
            club_id: clubDetail?.clubs?.id,
            user_id: user.id,
        });
        if (res) {
            actions.setAppLoader(false);
            await refreshUser();
            const updatedClubs = clubs.map((club: MapClub) =>
                club?.id === clubDetail?.clubs?.id ? { ...club, isFavorited: active } : club,
            );
            setMapClubs(updatedClubs);
            handleBoundriesChange();
        }
    }

    async function handlePlayedClub(active: boolean) {
        actions.setAppLoader(true);
        let res = await client.request(UPDATE_USER, {
            user_id: user.id,
            user: {
                playedClubs: active
                    ? [...user.playedClubs, clubDetail?.clubs.id]
                    : user.playedClubs.filter((club_id: number) => club_id !== clubDetail?.clubs.id),
            },
        });
        if (res) {
            actions.setAppLoader(false);
            await refreshUser();
        }
    }

    const handleEditOffer = (offer: MapClubOffers) => {
        navigation.navigate('Edit Offer', {
            offer: offer,
            refresh: handleDetailScreenData,
        });
    };

    const handleDeleteOffer = (offer: MapClubOffers) => {
        const deleteOffer = () => {
            const deleteOfferParams = {
                userId: user?.id,
                offerId: offer?.id,
            };
            fetcher({
                endpoint: DELETE_OFFER,
                method: 'POST',
                body: deleteOfferParams,
            }).then((res) => {
                if (res?.status) {
                    handleDetailScreenData();
                } else {
                    showToast({});
                }
            });
        };
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: deleteOffer,
            popupHeader: 'Delete Offer',
            popupSubText: 'Are you sure you would like to delete your offer?',
            firstBtnLabel: 'Dismiss',
            secondBtnLabel: 'Delete',
        });
    };

    const handleContinue = () => {
        setShowRM(false);
        if (isAccepted) {
            client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: user?.additional_settings?.showAcceptRequestPopup,
                        showCreateRequestPopup: false,
                    },
                },
            });
        }
        setTimeout(() => {
            navigation.navigate('Request Against Offer', {
                offer: selectedOffer,
                callBack: handleDetailScreenData,
            });
        }, 100);
    };
    const handleModalClose = () => {
        setShowRM(false);
    };

    const canCreateOffer = async () => {
        actions.setAppLoader(true);
        const isClubMuted = getIsClubUnmute(club, user?.clubs);
        if (club) {
            fetcher({
                endpoint: canCreateOfferURL,
                method: 'POST',
                body: {
                    user_id: user?.id,
                },
            }).then((res) => {
                if (res?.canCreate) {
                    if (!isClubMuted) {
                        navigation.navigate('Create Offer', {
                            selectClubForOffer: clubDetail,
                            callBack: handleDetailScreenData,
                        });
                    }
                } else {
                    if (!isClubMuted) Alert.alert('', res?.message);
                }
                actions.setAppLoader(false);
            });
        }
    };

    const handleOfferPress = (item: MapClubOffers) => {
        CleverTap.recordEvent(constants.CLEVERTAP.CLICK_OFFER_IN_CLUB_DETAILS, {
            'Offer ID': item.offer_id,
            'Offer created for': item?.my_tg_group_id?.length ? 'My TG' : 'All',
            'Locator colour': club?.properties?.color,
            'Club name': clubDetail?.clubs.name,
            'Club Tier': clubDetail?.clubs.lowest_visible_tier,
            'Club Type': clubDetail?.clubs.club_type,
            'Club Address': clubDetail?.clubs.address,
            'User email': user.email,
            'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
            'User Membership': user.membership_plan?.name, // Active / Inactive
            'User City': user.stripe_customer_info.address.city,
            'User State': user.stripe_customer_info.address.state,
            'User Country': user.stripe_customer_info.address.country,
            'Club in Current Location or not': 'No', // Yes / No
        });
        navigation.navigate('OfferDetails', { offerID: item.offer_id, prevScreenCallBack: handleDetailScreenData });
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.tealRgb} />
            <View style={styles.container}>
                <HomeScreenCommonHeader title={route?.params?.comeFromOffer ? 'Offers' : 'Map'} hideToggleIcon={true} />
                <ScrollView
                    style={{
                        flex: 1,
                        marginBottom:
                            isShowCreateRequest() ||
                            //@ts-ignore
                            (clubDetail?.clubs?.club_type === 0 && ownClub.includes(clubDetail?.clubs?.id))
                                ? Spacing.SCALE_80
                                : 0,
                    }}
                    bounces={false}>
                    {clubDetail && (
                        <View
                            style={[
                                styles.mapContainer,
                                {
                                    height: isShowMapLarge ? height * 0.55 : height * 0.2,
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                },
                            ]}>
                            <MapboxGL.MapView
                                style={styles.mapView}
                                attributionEnabled={false}
                                compassEnabled={false}
                                compassFadeWhenNorth={false}
                                scaleBarEnabled={false}
                                rotateEnabled={false}
                                logoEnabled={false}
                                pitchEnabled={false}
                                scrollEnabled={false}
                                zoomEnabled={false}>
                                <MapboxGL.Camera
                                    animationDuration={Platform.OS === 'ios' ? 1000 : 2000}
                                    animationMode={Platform.OS === 'ios' ? 'linearTo' : 'moveTo'}
                                    zoomLevel={14}
                                    centerCoordinate={centerCoordinates}
                                />
                                <MapCluster
                                    clubs={[club].map((club: MapClub) => ({
                                        ...club,
                                        properties: {
                                            ...club?.properties,
                                            isSelected: club?.id === clubDetail?.clubs?.id,
                                        },
                                    }))}
                                    onItemPress={() => {}}
                                />
                            </MapboxGL.MapView>
                        </View>
                    )}
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={() => {
                            actions.setShouldDetailBottomSheetOpen(true);
                            navigation.goBack();
                        }}>
                        <Back />
                    </TouchableOpacity>
                    <Animated.View
                        style={[
                            styles.detailsContainer,
                            animatedOpacityStyle,
                            { marginTop: isShowMapLarge ? height * 0.5 : Spacing.SCALE_100 },
                        ]}>
                        {clubDetail && (
                            <MapDetailCard
                                clubDetail={clubDetail}
                                handleFavoriteClub={handleFavoriteClub}
                                handlePlayedClub={handlePlayedClub}
                                ownClub={ownClub}
                                club={club}
                                navigation={navigation}
                                clubOffers={clubOffers}
                                handleEditOffer={handleEditOffer}
                                handleCreateRequest={(isRequestAgainstOffer: boolean, offer: MapClubOffers | null) =>
                                    matchTokenActive({ isRequestAgainstOffer, offer })
                                }
                                handleDeleteOffer={handleDeleteOffer}
                                setSelectedOffer={setSelectedOffer}
                                gameReviewCount={gameReviewCount}
                                handleOfferPress={handleOfferPress}
                                prevScreenCallBack={route?.params?.prevScreenCallBack}
                            />
                        )}
                    </Animated.View>
                </ScrollView>
                {isShowCreateRequest() && clubDetail?.clubs?.club_type === 0 ? (
                    <Animated.View style={[styles.footer, animatedOpacityStyle]}>
                        <MapButton
                            label="Create Request"
                            buttonContainer={styles.btnContainerStyle}
                            btnText={styles.btnTextStyle}
                            onPress={() => matchTokenActive({ isRequestAgainstOffer: false, offer: null })}
                            //@ts-ignore
                            Icon={CreateRequestNewIconTeal}
                            isLoading={false}
                            isIncreaseIconSize={false}
                        />
                    </Animated.View>
                ) : null}
                {
                    //@ts-ignore
                    clubDetail?.clubs?.club_type === 0 && ownClub.includes(clubDetail?.clubs?.id) && (
                        <Animated.View style={[styles.footer, animatedOpacityStyle]}>
                            <MapButton
                                label="Create Offer"
                                buttonContainer={styles.btnContainerStyle}
                                btnText={styles.btnTextStyle}
                                onPress={canCreateOffer}
                                //@ts-ignore
                                Icon={CreateOfferIconTeal}
                                isLoading={false}
                                isIncreaseIconSize={true}
                            />
                        </Animated.View>
                    )
                }
            </View>
            <CustomRM
                isVisible={showRM}
                label={'Request'}
                heading="Create a Request?"
                data={[
                    `Make a request, not a demand`,
                    `Endear yourself with a nice note, make the host want to host you`,
                    `Don't fish`,
                ]}
                isChecked={isAccepted}
                handleChecked={() => setIsAccepted(!isAccepted)}
                handleContinue={() => handleContinue()}
                handleCancel={() => handleModalClose()}
                handleBack={() => handleModalClose()}
            />
        </>
    );
};

export default MapDetails;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.screenBG,
    },
    mapContainer: {},
    mapView: {
        flex: 1,
        borderBottomLeftRadius: Size.SIZE_12,
        borderBottomRightRadius: Size.SIZE_12,
        overflow: 'hidden',
    },
    map: {
        flex: 1,
    },
    detailsContainer: {
        flex: 1,
        padding: 16,
        marginTop: Spacing.SCALE_100,
    },
    backButton: {
        position: 'absolute',
        top: 16,
        left: 16,
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 100,
    },
    footer: {
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_12,
        paddingHorizontal: Spacing.SCALE_16,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.whiteRGB,
        width: '100%',
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    btnContainerStyle: {
        width: '100%',
        marginBottom: Spacing.SCALE_12,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.whiteRGB,
    },
});
