import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Components imports
import OfferListing from './OfferListing';

// interface, utils, theme and type imports
import { MapClub, MapClubOffers } from '../../../../interface';
import { Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { RootStackParamList } from '../../../../interface/type';
import constants from '../../../../utils/constants/constants';

//CleverTap import
const CleverTap = require('clevertap-react-native');

const MapsClubOffer = ({
    clubOffers,
    navigation,
    handleEditOffer,
    handleCreateRequest,
    handleDeleteOffer,
    setSelectedOffer,
    club,
    handleOfferPress,
    prevScreenCallBack,
}: {
    clubOffers: MapClubOffers[];
    navigation: NativeStackNavigationProp<RootStackParamList>;
    handleEditOffer: (offer: MapClubOffers) => void;
    handleCreateRequest: (isRequestAgainstOffer: boolean, offer: MapClubOffers) => void;
    handleDeleteOffer: (offer: MapClubOffers) => void;
    setSelectedOffer: (offer: MapClubOffers) => void;
    club: MapClub;
    handleOfferPress: (item: MapClubOffers) => void;
    prevScreenCallBack?: () => void;
}) => {
    return clubOffers?.length ? (
        <View style={styles.container}>
            <View style={styles.headerContainer}>
                <Text style={styles.myTgText}>Open Offers ({clubOffers.length})</Text>
                {clubOffers?.length > 3 && (
                    <TouchableOpacity
                        onPress={() => {
                            if (prevScreenCallBack) prevScreenCallBack();
                            else {
                                CleverTap.recordEvent(constants.CLEVERTAP.VIEW_ALL);
                                navigation.navigate('Offers', {
                                    club: club,
                                });
                            }
                        }}>
                        <Text style={styles.viewAllText}>View All</Text>
                    </TouchableOpacity>
                )}
            </View>
            <FlatList
                data={clubOffers.slice(0, 3)}
                renderItem={({ item, index }) => (
                    <OfferListing
                        item={item}
                        navigation={navigation}
                        index={index}
                        handleEditOffer={handleEditOffer}
                        handleCreateRequest={handleCreateRequest}
                        handleDeleteOffer={handleDeleteOffer}
                        setSelectedOffer={setSelectedOffer}
                        handleOfferPress={handleOfferPress}
                    />
                )}
                keyExtractor={(item) => item.id.toString()}
            />
        </View>
    ) : null;
};

export default MapsClubOffer;

const styles = StyleSheet.create({
    container: {
        marginTop: Spacing.SCALE_20,
    },
    myTgText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    viewAllText: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_10,
    },
});
