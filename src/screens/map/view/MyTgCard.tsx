import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { SvgProps } from 'react-native-svg';

// utils, assets, interface and theme imports
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { MultiPeopleIcon, RequestAcceptedIconBlack, RightIcon, UserTickIcon } from '../../../assets/svg';
import { MapClub, MapClubDetail, User } from '../../../interface';
import { GlobalContext } from '../../../context/contextApi';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../interface/type';
import constants from '../../../utils/constants/constants';
//@ts-ignore
import { LocationContext } from '../../../context/LocationContext';

//CleverTap import
const CleverTap = require('clevertap-react-native');

const MyTgCard = ({
    clubDetail,
    user,
    navigation,
    club,
}: {
    clubDetail: MapClubDetail;
    user: User;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    club: MapClub;
}) => {
    const { actions, state } = useContext(GlobalContext);
    //@ts-ignore
    const { userLocation } = useContext(LocationContext);
    const {
        clubs,
        request,
        contacts,
        clubMemberCount,
        pnCount,
        friends,
        friendsPlayed,
        userPlayedAtClub,
        isEligibleForCreateRequest,
        tgGroupMembersCount,
        totalMemberCount,
    } = useMemo(() => {
        return clubDetail || {};
    }, [clubDetail]);

    const handleCleverTapEvent = (event: string) => {
        let eventObj = {
            'Club name': club.properties.name,
            'User’s Current Location': JSON.stringify(userLocation),
            'User email': user.email,
            'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
        };
        CleverTap.recordEvent(event, eventObj);
    };

    const handleViewFriends = () => {
        handleCleverTapEvent(constants.CLEVERTAP.FRIENDS_IN_THE_CLUB);
        actions?.setSelectedClub(clubDetail);
        navigation.navigate('MyFriendInClubScreen', { club: clubDetail, setSelectedClub: actions.setSelectedClub });
    };

    const handlePlayedFriends = () => {
        handleCleverTapEvent(constants.CLEVERTAP.FRIENDS_PLAYED_CLUB);
        actions?.setSelectedClub(club);
        navigation.navigate('PlayedFriendScreen', { club: clubDetail, setSelectedClub: actions.setSelectedClub });
    };

    const handleTGGroupMembers = () => {
        handleCleverTapEvent(constants.CLEVERTAP.GROUP_MEMBERS);
        actions?.setSelectedClub(club);
        navigation.navigate('TgGroupMembersScreen', { club: clubDetail, setSelectedClub: actions.setSelectedClub });
    };

    const [myTGList, setMyTGList] = useState<any[]>([
        {
            name: 'TG Group Members',
            Icon: MultiPeopleIcon,
            count: tgGroupMembersCount,
            onPress: handleTGGroupMembers,
        },
        {
            name: 'Friends Played',
            Icon: UserTickIcon,
            count: friendsPlayed,
            onPress: handlePlayedFriends,
        },
        {
            name: 'Total Friends',
            Icon: RequestAcceptedIconBlack,
            count: friends,
            onPress: handleViewFriends,
        },
    ]);

    useEffect(() => {
        const list = [...myTGList];
        setMyTGList(list.filter((item) => item.count));
    }, [tgGroupMembersCount, friendsPlayed, friends]);

    return tgGroupMembersCount || friendsPlayed || friends ? (
        <View style={styles.myTgContainer}>
            <Text style={styles.myTgText}>My TG</Text>
            <View style={styles.myTgContentContainer}>
                {myTGList.map(
                    (
                        {
                            name,
                            Icon,
                            count,
                            onPress,
                        }: {
                            name: string;
                            Icon: React.FC<SvgProps>;
                            count: number;
                            onPress: () => void;
                        },
                        index,
                    ) =>
                        count ? (
                            <>
                                <TouchableOpacity style={styles.myTgItem} key={`${index}-${name}`} onPress={onPress}>
                                    <View style={styles.rowStyle}>
                                        <View style={styles.myTgItemIconContainer}>
                                            <Icon />
                                        </View>
                                        <Text style={styles.myTgItemText}>{name}</Text>
                                    </View>
                                    <View style={[styles.rowStyle, { columnGap: Spacing.SCALE_10 }]}>
                                        <Text style={styles.myTgItemCount}>{count}</Text>
                                        <RightIcon />
                                    </View>
                                </TouchableOpacity>
                                {index !== myTGList.length - 1 && <View style={styles.divider} />}
                            </>
                        ) : null,
                )}
            </View>
        </View>
    ) : null;
};

export default MyTgCard;

const styles = StyleSheet.create({
    myTgContainer: {
        marginTop: Spacing.SCALE_20,
    },
    myTgContentContainer: {
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        paddingVertical: Spacing.SCALE_10,
    },
    myTgText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_10,
    },
    myTgItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_12,
    },
    myTgItemIconContainer: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_40,
        backgroundColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
    },
    myTgItemText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_17,
    },
    myTgItemCount: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    rowStyle: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_8,
        alignItems: 'center',
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightgray,
        marginVertical: Spacing.SCALE_10,
    },
});
