import { Al<PERSON>, FlatList, Pressable, StatusBar, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

//components and context imports
import MapButton from '../../../components/map/MapButton';
import FriendListInClub from './FriendListInClub';
import RequestScreenSkelton from '../../requests/view/RequestScreenSkelton';
import { PAGINATION_LIMIT } from '../../my-TG-Stream-Chat/client';
import showToast from '../../../components/toast/CustomToast';
import BroadCastPopup from '../played-friend screen/BroadCastPopup';
import { GlobalContext } from '../../../context/contextApi';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import { AuthContext } from '../../../context/AuthContext';

//utils, interfaces, assets, functions, services, hooks and theme imports
import fetchFriendList from './fetchFriendList';
import { BROADCAST_MESSAGE, GET_MAP_FRIEND_ID, tokenCheckURL } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import { CreateRequestIconWhite, Send } from '../../../assets/svg';
import { Friend, MapClubDetail, UserClub } from '../../../interface';
import { RootStackParamList } from '../../../interface/type';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

interface MyFriendInClubScreenProps {
    club?: MapClubDetail;
    setSelectedClub?: (club: MapClubDetail) => void;
}

const MyFriendInClubScreen = (props: { route: { params: MyFriendInClubScreenProps } }) => {
    const { club, setSelectedClub } = props?.route?.params;
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const [isPopupVisible, setIsPopupVisible] = useState(false);
    const [inputText, setInputText] = useState('');
    const [error, setError] = useState('');
    const [totalPage, setTotalPage] = useState();
    const [currentPage, setCurrentPage] = useState(1);
    const [friendList, setFriendList] = useState<Friend[]>([]);
    const [isMyClub, setIsMyClub] = useState(false);
    const [loading, setLoading] = useState({
        screenLoading: false,
        btnLoading: false,
    });
    const [nonMutedFriends, setNonMutedFriends] = useState<Friend[]>([]);

    // State to store all unmuted friends streamId
    const [allFriendsStreamId, setAllFriendStreamId] = useState<string[]>([]);

    // Get friend lis when user navigate to this screen
    useEffect(() => {
        getFriendList();
        return () => {
            //@ts-ignore
            setSelectedClub(state?.selectedClub);
        };
    }, []);

    // To get this club is mine or not
    useEffect(() => {
        user?.clubs?.map((myClub: UserClub) => {
            if (myClub?.club_id === club?.clubs?.id) {
                setIsMyClub(true);
            }
        });
    }, [user]);

    useEffect(() => {
        const nonMuted: Friend[] = friendList.filter((item) => {
            return !item.is_muted;
        });
        setNonMutedFriends(nonMuted);
    }, [friendList]);

    // Function to get friend list
    const getFriendList = async (isFirstPage = false) => {
        setLoading((prev) => ({ ...prev, screenLoading: true }));
        const payload = {
            userId: user?.id,
            search: club?.clubs?.name,
            page: isFirstPage ? 1 : currentPage,
            limit: PAGINATION_LIMIT,
        };
        const friendListRes = await fetchFriendList(payload);
        if (friendListRes?.status) {
            setLoading((prev) => ({ ...prev, screenLoading: false }));
            setFriendList([...friendList, ...friendListRes?.friends]);
            setCurrentPage(friendListRes?.currentPage);
            setTotalPage(friendListRes?.totalPages);
        } else {
            setLoading((prev) => ({ ...prev, screenLoading: false }));
            showToast({});
        }
    };

    // Send broadcast message
    const handleSendButton = () => {
        if (inputText) {
            setLoading((prev) => ({ ...prev, screenLoading: true }));
            fetcher({
                endpoint: BROADCAST_MESSAGE,
                method: 'POST',
                body: {
                    userId: user?.id,
                    channels: allFriendsStreamId,
                    message: inputText,
                    friendsOnly: true,
                },
            }).then((res) => {
                if (res?.status) {
                    setLoading((prev) => ({ ...prev, screenLoading: false }));
                    setInputText('');
                    setIsPopupVisible(!isPopupVisible);
                } else {
                    setLoading((prev) => ({ ...prev, screenLoading: false }));
                    showToast({});
                }
            });
        } else {
            setError('Please enter a valid message');
        }
    };

    // Check user can create request or not
    const checkUserCreateRequest = () => {
        setLoading((prev) => ({
            ...prev,
            screenLoading: true,
        }));
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body: {
                user_id: user?.id,
            },
        }).then((res) => {
            setLoading((prev) => ({
                ...prev,
                screenLoading: false,
            }));
            if (res?.canCreate)
                navigation.navigate('CreateRequestMap', {
                    club,
                    createOneToOneRequest: false,
                    friend_id: '',
                    screen: 'Friends Found',
                });
            else {
                Alert.alert('', res?.message);
            }
        });
    };

    // This function will handle to get all friends stream id and open bottomSheet for send broadcast message
    const handleOpenBottomSheetMessageToAll = () => {
        actions.setAppLoader(true);
        const payload = {
            userId: user?.id,
            search: club?.clubs?.name,
        };
        fetcher({
            endpoint: GET_MAP_FRIEND_ID,
            method: 'POST',
            body: payload,
        }).then((res) => {
            try {
                if (res?.status) {
                    actions.setAppLoader(false);
                    let tempFriend = res.friends.map(({ stream_channel_id }: Friend) => stream_channel_id);
                    setAllFriendStreamId([...tempFriend]);
                    setIsPopupVisible((prev) => !prev);
                } else {
                    actions.setAppLoader(false);
                    showToast({});
                }
            } catch (error) {
                actions.setAppLoader(false);
                showToast({});
            }
        });
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={{ backgroundColor: colors.whiteRGB, flex: 1 }}>
                <ProfileHeader
                    title={'My Friends'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                />
                {loading.screenLoading ? (
                    <RequestScreenSkelton screen="Common" />
                ) : (
                    <View style={styles.container}>
                        {/* UI of sub header */}
                        <View style={styles.subHeader}>
                            <Text style={styles.totalFriendStyle}>Total Friends: {friendList?.length}</Text>
                            <Pressable style={styles.box1} onPress={handleOpenBottomSheetMessageToAll}>
                                <Text style={styles.sendMessageToStyle}>Send Message to all</Text>
                                <Send />
                            </Pressable>
                        </View>
                        <FlatList
                            data={friendList}
                            renderItem={(data) => (
                                <FriendListInClub
                                    friendList={data}
                                    isMyClub={isMyClub}
                                    club={club || ({} as MapClubDetail)}
                                />
                            )}
                            style={{ paddingHorizontal: Spacing.SCALE_16 }}
                        />
                    </View>
                )}
            </View>
            {!isMyClub && nonMutedFriends.length > 0 && !loading.screenLoading && (
                <View style={styles.btnWrapper}>
                    <MapButton
                        label="Create Request to all"
                        buttonContainer={styles.btnContainer}
                        btnText={styles.btnTextStyle}
                        onPress={checkUserCreateRequest}
                        //@ts-ignore
                        Icon={CreateRequestIconWhite}
                        isLoading={loading?.btnLoading}
                        isIncreaseIconSize={false}
                    />
                </View>
            )}
            {isPopupVisible && (
                <BroadCastPopup
                    popupState={[isPopupVisible, setIsPopupVisible]}
                    inputState={[inputText, setInputText]}
                    handleSendButton={handleSendButton}
                    errorState={[error, setError]}
                />
            )}
        </>
    );
};

export default MyFriendInClubScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.screenBG,
        paddingTop: Spacing.SCALE_12,
    },
    subHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_15,
        marginBottom: Spacing.SCALE_8,
    },
    box1: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    totalFriendStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    sendMessageToStyle: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    btnContainer: {
        paddingHorizontal: Spacing.SCALE_15,
        marginBottom: Spacing.SCALE_10,
    },
    btnWrapper: {
        backgroundColor: colors.whiteRGB,
        height: Size.SIZE_80,
        justifyContent: 'center',
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.whiteRGB,
    },
});
