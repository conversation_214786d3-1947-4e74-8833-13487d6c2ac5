import React, { useContext, useEffect, useState } from 'react';
import { AppState, PermissionsAndroid, Alert, Linking, Platform } from 'react-native';
import { Button, Icon } from 'react-native-elements';
import { LocationContext } from '../../context/LocationContext';

const UserLocationButton = ({ setUserRegion }) => {

    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false)

    const { userLocation, getLocation } = useContext(LocationContext);

    const checkAndroidPerms = () => PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION).then(permission => {
        setLocationPermissionGranted(permission)
    }).catch(err => console.log({ err }))

    useEffect(() => {
        if (Platform.OS === 'ios') {
            setLocationPermissionGranted(true)
        } else {
            checkAndroidPerms()

            // AppState.addEventListener("change", _handleAppStateChange);

            // return () => {
            //     AppState.removeEventListener("change", _handleAppStateChange);
            // };
        }
    }, []);
    useEffect(() => {
        if (locationPermissionGranted && !userLocation) {
            getLocation()
        }
    }, [locationPermissionGranted, userLocation])

    const onButtonPress = () => {
        console.log("UserLocationButton: Button pressed, userLocation:", userLocation);
        
        if (!locationPermissionGranted) {
            console.log('UserLocationButton: No location permission');
            // Show alert to enable location permissions
            if (Platform.OS === 'ios') {
                Alert.alert(
                    "Location Permission Required",
                    "Please enable location services for this app in your device settings.",
                    [{ text: "OK", onPress: () => Linking.openSettings() }]
                );
            }
        } else if (!userLocation || !userLocation.latitude || !userLocation.longitude) {
            console.log('UserLocationButton: No valid location, requesting again');
            getLocation();
            
            // Use default US location after a short delay if still no location
            setTimeout(() => {
                if (!userLocation || !userLocation.latitude || !userLocation.longitude) {
                    console.log('UserLocationButton: Using default US location');
                    // Call setUserRegion which will handle the default location
                    setUserRegion();
                } else {
                    setUserRegion();
                }
            }, 1000);
        } else {
            console.log('UserLocationButton: Valid location, setting user region');
            setUserRegion();
        }
    };

    const locateIcon = () => (
        <Icon
            type='material-community'
            name='crosshairs-gps'
            size={25}
            style={{ padding: 5 }}
        />
    );

    return (
        <Button
            onPress={setUserRegion}
            icon={locateIcon}
            buttonStyle={styles.buttonStyle}
            containerStyle={styles.containerStyle}
        />
    );
};

const styles = {
    buttonStyle: {
        backgroundColor: 'white',
        borderRadius: 50
    },
    containerStyle: {
        position: 'absolute',
        borderRadius: 50,
        bottom: 120,
        right: '5%',
    }
};

export default UserLocationButton;
