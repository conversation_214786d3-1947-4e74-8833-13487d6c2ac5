import React, { memo } from 'react';
import { SymbolLayer, ShapeSource, Images } from '@rnmapbox/maps';
import { OnPressEvent } from '@rnmapbox/maps/lib/typescript/src/types/OnPressEvent';

//Assets, Interfaces, Constants, Utils and Theme
import TealClub from '../../assets/images/club/teal.png';
import TealContact from '../../assets/images/contact/teal.png';
import GreenClub from '../../assets/images/club/green.png';
import GreenContact from '../../assets/images/contact/green.png';
import BlueClub from '../../assets/images/club/blue.png';
import BlueContact from '../../assets/images/contact/blue.png';
import GrayClub from '../../assets/images/club/grey.png';
import GrayContact from '../../assets/images/contact/grey.png';
import YellowClub from '../../assets/images/club/yellow.png';
import YellowContact from '../../assets/images/contact/yellow.png';
import ClusterMarker from '../../assets/images/clusternewIcon.png';
import {
    BLUE,
    BLUE_CONTACT,
    GREEN,
    GREEN_CONTACT,
    GREY,
    GREY_CONTACT,
    TEAL,
    TEAL_CONTACT,
    YELLOW,
    YELLOW_CONTACT,
} from '../../screens/my-TG-Stream-Chat/client';
import { colors } from '../../theme/theme';
import { MapClub } from '../../interface';

const MapCluster = ({ clubs, onItemPress }: { clubs: MapClub[]; onItemPress: (event: OnPressEvent) => void }) => {
    return (
        <ShapeSource
            onPress={onItemPress}
            id="clubMarkersSource"
            shape={{
                type: 'FeatureCollection',
                features: clubs || [],
            }}
            cluster={true}
            clusterMaxZoomLevel={5}
            clusterRadius={40}
            tolerance={0.45}>
            <SymbolLayer
                id="tealClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', TEAL]]}
                style={{
                    iconImage: TealClub,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[TealClub]} />
            <SymbolLayer
                id="tealContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', TEAL_CONTACT]]}
                style={{
                    iconImage: TealContact,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[TealContact]} />

            <SymbolLayer
                id="greenClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREEN]]}
                style={{
                    iconImage: GreenClub,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[GreenClub]} />

            <SymbolLayer
                id="greenContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREEN_CONTACT]]}
                style={{
                    iconImage: GreenContact,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[GreenContact]} />

            <SymbolLayer
                id="blueClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', BLUE]]}
                style={{
                    iconImage: BlueClub,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[BlueClub]} />

            <SymbolLayer
                id="blueContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', BLUE_CONTACT]]}
                style={{
                    iconImage: BlueContact,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[BlueContact]} />

            <SymbolLayer
                id="greyClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREY]]}
                style={{
                    iconImage: GrayClub,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[GrayClub]} />

            <SymbolLayer
                id="greyContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREY_CONTACT]]}
                style={{
                    iconImage: GrayContact,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[GrayContact]} />

            <SymbolLayer
                id="yellowClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', YELLOW]]}
                style={{
                    iconImage: YellowClub,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[YellowClub]} />

            <SymbolLayer
                id="yellowContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', YELLOW_CONTACT]]}
                style={{
                    iconImage: YellowContact,
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 12, ['get', 'name']],
                    textSize: 12,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.black,
                }}
            />
            <Images nativeAssetImages={[YellowContact]} />

            <SymbolLayer
                id="cluster-count"
                // @ts-ignore
                type="symbol"
                filter={['all', ['has', 'point_count']]}
                style={{
                    iconImage: ClusterMarker,
                    iconSize: 0.4,
                    textField: '{point_count}',
                    textSize: 10,
                    iconAllowOverlap: true,
                    iconOffset: [0, -5],
                }}
            />
            <Images nativeAssetImages={[ClusterMarker]} />
        </ShapeSource>
    );
};

export default memo(MapCluster);
