import { useCallback, useContext, useEffect, useState } from 'react';
import { GlobalContext } from '../context/contextApi';
import { FilterState, MapClub, Offer, User } from '../interface';
import { autocomplete, GET_CLUBS_WITH_OFFERS } from '../service/EndPoint';
import { fetcher } from '../service/fetcher';
import debounce from '../screens/my-TG-Stream-Chat/action';
import { ALL } from '../utils/constants/strings';
import { BLUE_CONTACT } from '../screens/my-TG-Stream-Chat/client';
import { BLUE } from '../screens/my-TG-Stream-Chat/client';

interface DateRange {
    startDate: string;
    endDate: string;
}

const useOfferState = ({ user }: { user: User }) => {
    const { state } = useContext(GlobalContext);
    const [activeView, setActiveView] = useState('MAP');
    const [selectedClub, setSelectedClub] = useState<MapClub | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [triggerSearchString, setTriggerSearchString] = useState('');
    const [zoomLevel, setZoomLevel] = useState(12);
    const [centerCoordinates, setCenterCoordinates] = useState<[number, number]>([
        state?.mapCurrentState?.lng || 37.0902,
        state?.mapCurrentState?.lat || 95.7129,
    ]);
    const [showSearch, setShowSearch] = useState(false);
    const [offersMapMarkers, setOffersMapMarkers] = useState<MapClub[]>([]);
    const [mapBoundaries, setMapBoundaries] = useState([]);
    const [locations, setLocations] = useState([]);
    const [searchedClubList, setSearchedClubList] = useState<MapClub[]>([]);
    const [selectedRegion, setSelectedRegion] = useState<{ lat: number; lng: number } | null>({
        lat: 0,
        lng: 0,
    });
    const [dateRange, setDateRange] = useState<DateRange>({ startDate: '', endDate: '' });
    const [offers, setOffers] = useState<Offer[]>([]);
    const [renderedOffers, setRenderedOffers] = useState<MapClub[]>([]);
    const [searchType, setSearchType] = useState<string>('');
    const [showFilterOption, setShowFilterOption] = useState(false);
    const [playedCount, setPlayedCount] = useState(0);
    const [tierCount, setTierCount] = useState({
        Fern: 0,
        Sage: 0,
        Moss: 0,
        Olive: 0,
    });
    const [femaleCount, setFemaleCount] = useState(0);
    const [playAsCoupleCount, setPlayAsCoupleCount] = useState(0);
    const [favoriteCount, setFavoriteCount] = useState(0);
    const [searchedClub, setSearchedClub] = useState<MapClub | null>(null);
    const [isMarkerClicked, setIsMarkerClicked] = useState(false);
    const [filter, setFilter] = useState<FilterState>({
        fern: false,
        sage: false,
        moss: false,
        olive: false,
        friendsAndContact: false,
        myTGGroupMember: false,
        selectedTGGroup: [],
        openOfferClubs: false,
        favoriteClubs: false,
        playedClubs: false,
        playAsCouple: false,
        clubswithFemaleMembers: false,
        clubPercentage: ALL,
        clubMemberCount: ALL,
    });
    const [clubs, setClubs] = useState<MapClub[]>([]);
    const [filterActive, setFilterActive] = useState(false);

    useEffect(() => {
        if (offersMapMarkers) {
            getCounts(offersMapMarkers);
        }
    }, [offersMapMarkers]);

    useEffect(() => {
        if (!selectedClub) {
            handleBoundriesChange();
            return;
        }
    
        if (isMarkerClicked) {
            const clubOffers = offersMapMarkers?.find(
                (club: MapClub) => club.id === Number(selectedClub.id)
            );
            if (clubOffers) {
                setRenderedOffers([clubOffers]);
            }
        } else {
            handleBoundriesChange();
        }
    }, [selectedClub, isMarkerClicked, offersMapMarkers]);

    useEffect(() => {
        if (offersMapMarkers) {
            // const filteredOffers = filterOfferAccordingToDateRange(offersMapMarkers);
            // console.log("filteredOffers", filteredOffers)
        }
    }, [dateRange, offersMapMarkers]);

    useEffect(() => {
        if (mapBoundaries && !isMarkerClicked) {
            handleBoundriesChange();
        }
    }, [mapBoundaries]);

    useEffect(() => {
        getMapClubs();
    }, [user]);

    useEffect(() => {
        if (searchTerm?.length > 0) {
            updateTriggerSearchStringUpdate(searchTerm);
        }
    }, [searchTerm]);

    const updateTriggerSearchStringUpdate = useCallback(
        debounce((value: string) => {
            getLocations(value);
            handleSearchClub(value);
        }, 300),
        [offersMapMarkers],
    );

    const getLocations = (searchTerm: string) => {
        fetch(`${autocomplete}${searchTerm}`)
            .then((data) =>
                data.json().then(({ predictions }) => {
                    if (searchTerm && searchTerm.length) {
                        if (predictions && predictions.length > 0) {
                            setLocations(predictions);
                        }
                    } else setLocations([]);
                }),
            )
            .catch((err) => {});
    };

    const handleSearchClub = (searchTerm: string) => {
        const tempClubs: MapClub[] = offersMapMarkers?.filter((club: MapClub) => {
            return club.properties.name.toLowerCase().includes(searchTerm?.trim()?.toLowerCase());
        });
        setSearchedClubList(tempClubs);
    };

    const getMapClubs = () => {
        const body = {
            userId: user?.id,
        };
        fetcher({
            endpoint: GET_CLUBS_WITH_OFFERS,
            method: 'POST',
            body,
        })
            .then((data) => {
                setOffersMapMarkers(data?.clubs);
                setClubs(data?.clubs);
            })
            .catch((err) => {
                console.log('err', err);
            });
    };

    const handleBoundriesChange = () => {
        let boundariesClub: MapClub[] = [];
        offersMapMarkers?.map((club: MapClub) => {
            if (
                club.geometry?.coordinates &&
                mapBoundaries?.[0] &&
                mapBoundaries?.[1] &&
                // @ts-ignore
                club.geometry.coordinates[1] <= mapBoundaries[0][1] &&
                // @ts-ignore
                club.geometry.coordinates[1] >= mapBoundaries[1][1] &&
                // @ts-ignore
                club.geometry.coordinates[0] <= mapBoundaries[0][0] &&
                // @ts-ignore
                club.geometry.coordinates[0] >= mapBoundaries[1][0]
            ) {
                boundariesClub.push(club);
            }
        });
        setRenderedOffers(boundariesClub);
    };

    const getCounts = (clubs: MapClub[]) => {
        clubs.forEach((club) => {
            if (club.properties.played) {
                setPlayedCount((prev) => prev + 1);
            }
            if (club.properties.tier === 0 || club.properties.tier === 1) {
                setTierCount((prev) => ({ ...prev, Fern: prev.Fern + 1 }));
            }
            if (club.properties.tier === 2) {
                setTierCount((prev) => ({ ...prev, Sage: prev.Sage + 1 }));
            }
            if (club.properties.tier === 3) {
                setTierCount((prev) => ({ ...prev, Moss: prev.Moss + 1 }));
            }
            if (club.properties.tier === 5) {
                setTierCount((prev) => ({ ...prev, Olive: prev.Olive + 1 }));
            }
            if (club.properties.hasFemalMember) {
                setFemaleCount((prev) => prev + 1);
            }
            if (club.properties.hasPlayAsCoupleMember) {
                setPlayAsCoupleCount((prev) => prev + 1);
            }
            if (club.properties.favClub) {
                setFavoriteCount((prev) => prev + 1);
            }
        });
    };

    function getVisibleTiers() {
        let tiers = [];
        if (!filter.fern && !filter.sage && !filter.moss && !filter.olive) {
            tiers.push(0);
            tiers.push(1);
            tiers.push(2);
            tiers.push(3);
            tiers.push(5);
        } else {
            if (filter.fern) {
                tiers.push(0);
                tiers.push(1);
            }
            if (filter.sage) tiers.push(2);
            if (filter.moss) tiers.push(3);
            if (filter.olive) tiers.push(5);
        }
        return tiers;
    }

    useEffect(() => {
        handleMapFilter();
    }, [filter, clubs]);

    //This function is used to filter the value
    const handleMapFilter = () => {
        if (filterActive) {
            let tempClubs = [],
                tgFilterFriendsClub: MapClub[] = [],
                tgFilterGroupMemberClub: MapClub[] = [];
            //Filter clubs on the basis of tier
            const visibleTiers = getVisibleTiers();
            tempClubs = offersMapMarkers?.filter((club: MapClub) => visibleTiers?.includes(club?.properties?.tier));

            //Filter clubs on the basis of TG Community
            if (filter?.friendsAndContact) {
                tgFilterFriendsClub = tempClubs?.filter((club: MapClub) => {
                    return club?.properties?.isFriend || club?.properties?.isContact;
                });
            }

            if (filter?.myTGGroupMember) {
                if (!filter?.selectedTGGroup?.length) {
                    tgFilterGroupMemberClub = tempClubs?.filter((club: MapClub) => {
                        return club?.properties?.isMyTgGroupMember;
                    });
                } else {
                    let selectedGroupId = filter?.selectedTGGroup.map((id: string) => id);
                    tgFilterGroupMemberClub = tempClubs?.filter((club: MapClub) => {
                        return !!club?.properties?.myTgGroupIds?.filter((e: any) => selectedGroupId.indexOf(e) !== -1)
                            ?.length;
                    });
                }
            }

            if (filter?.friendsAndContact || filter?.myTGGroupMember) {
                tempClubs = [...tgFilterFriendsClub, ...tgFilterGroupMemberClub];
            }

            if (
                filter?.openOfferClubs ||
                filter?.favoriteClubs ||
                filter?.playedClubs ||
                filter?.playAsCouple ||
                filter?.clubswithFemaleMembers
            ) {
                //User fav clubs
                const userFavClubs = user.favorite_clubs.map((club) => club.club_id);

                //Other filters
                tempClubs = tempClubs?.filter((club: MapClub) => {
                    //Club has open offers
                    if (
                        filter?.openOfferClubs &&
                        (club?.properties?.color === BLUE || club?.properties?.color === BLUE_CONTACT)
                    ) {
                        return true;
                    }

                    //Favorite clubs
                    if (filter?.favoriteClubs && userFavClubs.includes(club.id)) {
                        return true;
                    }

                    //Played clubs
                    if (filter?.playedClubs && user?.playedClubs?.includes(club.id)) {
                        return true;
                    }

                    //Play as Couple
                    if (filter?.playAsCouple && club?.properties?.hasPlayAsCoupleMember) {
                        return true;
                    }

                    //Clubs with Female Members
                    if (filter?.clubswithFemaleMembers && club?.properties.hasFemalMember) {
                        return true;
                    }

                    return false;
                });
            }

            setOffersMapMarkers(tempClubs);
        } else {
            setOffersMapMarkers(clubs);
        }
    };

    return {
        activeView,
        setActiveView,
        searchTerm,
        setSearchTerm,
        zoomLevel,
        setZoomLevel,
        centerCoordinates,
        setCenterCoordinates,
        showSearch,
        setShowSearch,
        offersMapMarkers,
        mapBoundaries,
        setMapBoundaries,
        selectedClub,
        setSelectedClub,
        searchedClubList,
        locations,
        triggerSearchString,
        setTriggerSearchString,
        selectedRegion,
        setSelectedRegion,
        dateRange,
        setDateRange,
        offers,
        renderedOffers,
        setRenderedOffers,
        searchType,
        setSearchType,
        showFilterOption,
        setShowFilterOption,
        getMapClubs,
        playedCount,
        tierCount,
        femaleCount,
        playAsCoupleCount,
        searchedClub,
        setSearchedClub,
        isMarkerClicked,
        setIsMarkerClicked,
        filter,
        setFilter,
        favoriteCount,
        filterActive,
        setFilterActive,
    };
};

export default useOfferState;
