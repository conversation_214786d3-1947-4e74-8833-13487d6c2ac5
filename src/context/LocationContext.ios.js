import React, { createContext, useEffect, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import Geolocation from '@react-native-community/geolocation';

const LocationContext = createContext();

export { LocationContext };

export default function LocationProvider({ children }) {
  const [userLocation, setUserLocation] = useState(null);

  const getLocation = () => {
    console.log("LocationContext iOS: Getting location");
    
    // Configure geolocation
    Geolocation.setRNConfiguration({
      skipPermissionRequests: false,
      authorizationLevel: 'whenInUse',
      locationProvider: 'auto'
    });
    
    // Get current position with more generous timeout
    Geolocation.getCurrentPosition(
      ({ coords: { latitude, longitude } }) => {
        console.log("LocationContext iOS: Got location:", latitude, longitude);
        const loc = {
          latitude,
          longitude,
        };
        setUserLocation(loc);
      },
      err => {
        console.log('LocationContext iOS: Error getting location:', err);
        // Use default US location as fallback
        const loc = {
          latitude: 37.090240,
          longitude: -95.712891,
        };
        console.log('LocationContext iOS: Using fallback location:', loc);
        setUserLocation(loc);
      },
      { 
        enableHighAccuracy: true, 
        timeout: 10000, 
        maximumAge: 10000 
      }
    );

    // Set up watch position
    Geolocation.watchPosition(
      ({ coords: { latitude, longitude } }) => {
        console.log("LocationContext iOS: Location update:", latitude, longitude);
        const loc = {
          latitude,
          longitude,
        };
        setUserLocation(loc);
      }, 
      err => console.log('LocationContext iOS: Watch position error:', err),
      {
        enableHighAccuracy: true,
        distanceFilter: 100,
        interval: 5000,
        fastestInterval: 2000
      }
    );
  }

  useEffect(() => {
    // Initialize location services when component mounts
    getLocation();
    
    // Set a fallback timer to ensure we have some location
    const fallbackTimer = setTimeout(() => {
      if (!userLocation) {
        console.log('LocationContext iOS: No location after timeout, using fallback');
        setUserLocation({
          latitude: 37.090240,
          longitude: -95.712891,
        });
      }
    }, 5000);
    
    // Cleanup function
    return () => {
      clearTimeout(fallbackTimer);
      Geolocation.clearWatch(null);
      Geolocation.stopObserving();
    };
  }, []);

  return (
    <LocationContext.Provider value={{ userLocation, setUserLocation, getLocation }}>
      {children}
    </LocationContext.Provider>
  );
}
